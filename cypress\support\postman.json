{"schemes": ["http", "https"], "swagger": "2.0", "info": {"description": "Platform API to better connect frontend solutions with Pragmatic.", "title": "Platform-API", "contact": {}, "version": "0.1"}, "host": "", "basePath": "", "paths": {"/api/cashier/deposit/wallet-payment": {"post": {"description": "<p> Make a deposit to the user wallet according the payment method and amount.</p>\nError codes: 4: Internal policies | 10: Internal Server Error | 11: Bad request | 12: Missing or invalid fields | 14: Invalid Data | 19: Invalid User Profile | 22: Invalid Authetication\n23: Invalid partner id | 24: Invalid Grant Type | 25: Missing PartnerId | 53: Payment Method Not Allowed | 54: Payment Blocked | 55: Limit Exceeded | 56: Payment Error | 57: Insuficient Balance\n58: Third-party Fail", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Cashier V3"], "summary": "Make deposit to the wallet.", "parameters": [{"type": "string", "description": "Current Pragmatic user session id", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}, {"description": "Deposit information data", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_adapters_inbound_api_dto.Deposit"}}], "responses": {"200": {"description": "Payment information data with link and QRCode", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.DepositResponse"}}, "400": {"description": "Bad request to make a deposit", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "401": {"description": "User session expired or user not authorized", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "409": {"description": "Error with the user", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "422": {"description": "Error in deposit action", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "424": {"description": "Third-party error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/cashier/deposit/account": {"get": {"description": "<p> Handles the request to get user account details screen information according the required platform (web or mobile) </p>\nError Codes: 10: Internal Server Error | 11: Bad Request| 14: Invalid Data | 15: Invalid Session | 58: Third-party Fail", "produces": ["application/json"], "tags": ["Cashier V3"], "summary": "User Account Details", "parameters": [{"type": "string", "name": "deposit", "in": "query"}, {"type": "boolean", "name": "verified", "in": "query"}, {"type": "string", "description": "Current pragmatic user session id", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Deposit screen info for the platform", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.DepositScreenInfo"}}, "204": {"description": "No account available for the filter"}, "400": {"description": "Bad request, invalid filter param or session id header not found", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "401": {"description": "User not authorized or session expired", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "409": {"description": "Error with the user", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "424": {"description": "Third-party error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/cashier/deposit/queue-notification": {"post": {"description": "<p> Make a withdrawal from the user wallet according the payment method and amount.</p>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Cashier V3"], "summary": "Make transaction push notification.", "parameters": [{"type": "string", "description": "Current Pragmatic user session id", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}, {"description": "transaction push notification data", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_adapters_inbound_api_dto.Transaction"}}], "responses": {"200": {"description": "transaction push notification information data", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.WithdrawalResponse"}}, "400": {"description": "Bad request to make a withdrawal", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "401": {"description": "User session expired or user not authorized", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "409": {"description": "Error with the user", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "422": {"description": "Error in withdrawal action", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "424": {"description": "Third-party error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/cashier/deposit/queued-notifications": {"get": {"description": "<p>Retrieves all transaction notifications currently queued in the system.</p>", "produces": ["application/json"], "tags": ["Cashier V3"], "summary": "List all queued transaction notifications", "parameters": [{"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "List of queued transaction notifications", "schema": {"type": "array", "items": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.Transaction"}}}, "204": {"description": "No queued notifications found"}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}, "delete": {"description": "<p>Removes all transaction notifications currently queued in the system.</p>", "produces": ["application/json"], "tags": ["Cashier V3"], "summary": "Clear all queued transaction notifications", "parameters": [{"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Success response", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/cashier/deposit/screen-info": {"get": {"description": "<p> Handles the request to get deposit screen information according the required platform (web or mobile) </p>\nError Codes: 10: Internal Server Error | 11: Bad Request| 14: Invalid Data | 15: Invalid Session | 58: Third-party Fail", "produces": ["application/json"], "tags": ["Cashier V3"], "summary": "Deposit Screen Information", "parameters": [{"enum": ["web", "mobile"], "type": "string", "description": "Platform to get deposit screen data.", "name": "platform", "in": "query", "required": true}, {"type": "string", "description": "Current pragmatic user session id", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Deposit screen info for the platform", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.DepositScreenInfo"}}, "204": {"description": "No deposit screen info is available for the platform"}, "400": {"description": "Bad request, invalid platform param or session id header not found", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "401": {"description": "User not authorized or session expired", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "409": {"description": "Error with the user", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "424": {"description": "Third-party error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/cashier/deposit/update-bank-account": {"post": {"description": "Update a user's bank account with the provided details. If successful, returns the updated bank account and session data.\n<p>Possible error scenarios and corresponding codes:</p>\n<ul>\n<li><b>10</b>: Internal Server Error</li>\n<li><b>11</b>: Bad Request</li>\n<li><b>12</b>: Missing or Invalid Fields</li>\n<li><b>13</b>: Self-Excluded (GamStop)</li>\n<li><b>14</b>: Invalid Data</li>\n<li><b>15</b>: Invalid Session</li>\n</ul>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Cashier V3"], "summary": "Update user bank account and start a new session", "parameters": [{"description": "User bank account", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.UpdateBankAccount"}}, {"type": "string", "default": "swagger", "description": "Identification of the application accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "description": "Session ID for the current user session", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Bank account successfully updated or add", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.UpdateBankAccountResponse"}}, "400": {"description": "Invalid request body", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "409": {"description": "Bank account  data not accepted", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/cashier/deposit/wallet-payment": {"post": {"description": "<p> Make a deposit to the user wallet according the payment method and amount.</p>\nError codes: 4: Internal policies | 10: Internal Server Error | 11: Bad request | 12: Missing or invalid fields | 14: Invalid Data | 19: Invalid User Profile | 22: Invalid Authetication\n23: Invalid partner id | 24: Invalid Grant Type | 25: Missing PartnerId | 53: Payment Method Not Allowed | 54: Payment Blocked | 55: Limit Exceeded | 56: Payment Error | 57: Insuficient Balance\n58: Third-party Fail", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Cashier V3"], "summary": "Make deposit to the wallet.", "parameters": [{"type": "string", "description": "Current Pragmatic user session id", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}, {"description": "Deposit information data", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_adapters_inbound_api_dto.Deposit"}}], "responses": {"200": {"description": "Payment information data with link and QRCode", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.DepositResponse"}}, "400": {"description": "Bad request to make a deposit", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "401": {"description": "User session expired or user not authorized", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "409": {"description": "Error with the user", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "422": {"description": "Error in deposit action", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "424": {"description": "Third-party error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/cashier/deposit/wallet-withdrawal": {"post": {"description": "<p> Make a withdrawal from the user wallet according the payment method and amount.</p>\nError codes: 4: Internal policies | 10: Internal Server Error | 11: Bad request | 12: Missing or invalid fields | 14: Invalid Data | 19: Invalid User Profile | 22: Invalid Authetication\n23: Invalid partner id | 24: Invalid Grant Type | 25: Missing PartnerId | 54: Payment Blocked | 55: Limit Exceeded | 56: Payment Error | 57: Insuficient Balance\n58: Third-party Fail", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Cashier V3"], "summary": "Make withdrawal from the wallet.", "parameters": [{"type": "string", "description": "Current Pragmatic user session id", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}, {"description": "Withdrawal information data", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_adapters_inbound_api_dto.Withdrawal"}}], "responses": {"200": {"description": "Withdrawal information data", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.WithdrawalResponse"}}, "400": {"description": "Bad request to make a withdrawal", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "401": {"description": "User session expired or user not authorized", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "409": {"description": "Error with the user", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "422": {"description": "Error in withdrawal action", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "424": {"description": "Third-party error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/games/cache/all": {"delete": {"description": "<p> Clears all cached data </p>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Games V3"], "summary": "Clear All Cache", "parameters": [{"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Successfully cleared all cache", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.SuccessResponse"}}, "500": {"description": "Failed to clear cache", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/games/get-free-games": {"get": {"description": "<p> Get free games based on a given query </p>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Games V3"], "summary": "Get Free Games", "parameters": [{"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}, {"type": "string", "name": "gameSymbol", "in": "query"}, {"type": "string", "name": "language", "in": "query"}, {"type": "boolean", "name": "upcomingGames", "in": "query"}], "responses": {"200": {"description": "Successfully", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.GameResponse"}}, "400": {"description": "Invalid query", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Request failed", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/games/get-group-games": {"get": {"description": "<p> Get group games based on a given gameType </p>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Games V3"], "summary": "Get Group Games", "parameters": [{"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}, {"type": "array", "items": {"type": "integer"}, "collectionFormat": "csv", "name": "gameCodes", "in": "query"}, {"type": "string", "name": "language", "in": "query"}], "responses": {"200": {"description": "Successfully", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.GameGroupShortResponse"}}, "400": {"description": "Invalid query", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Request failed", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/games/get-list-group": {"get": {"description": "<p> Get group games based on a given gameType </p>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Games V3"], "summary": "Get Group Games", "responses": {"200": {"description": "Successfully", "schema": {"type": "array", "items": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.LobbyWithGameGroups"}}}, "400": {"description": "Invalid query", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Request failed", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/games/get-lobby-group-games": {"get": {"description": "<p> Get lobby game group games based on a given gameType </p>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Games V3"], "summary": "Get Real Games", "parameters": [{"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}, {"type": "boolean", "name": "hasDemo", "in": "query"}, {"type": "string", "name": "language", "in": "query"}, {"type": "boolean", "name": "upcomingGames", "in": "query"}, {"enum": ["populares", "crashgames", "slots", "todos", "jogosdemesa", "no<PERSON><PERSON><PERSON>", "minigames", "jogosaovivo", "originais", "joguecom1", "roletas", "baccarat", "blackjack", "salasbrasile<PERSON><PERSON>", "esportes", "populares-jogos", "top-10-cassino", "top-10-<PERSON><PERSON><PERSON><PERSON><PERSON>", "10-ao-vivo", "animais", "ao-vivo-recomenda-old", "bingo", "bingo-jogos-ao-vivo", "bingo-ao-vivo", "bloco-da-estrela", "cartas", "comprar-bonus", "drop-and-wins", "drop-wins", "em-breve", "especiais-da-estrela", "especial-pascoa", "estrela-indica", "estrela-bet-friday-cashback-e<PERSON><PERSON>", "evolution", "festival-de-premios", "first-person", "first-person-evolution", "fortune-games", "frutas", "futebol", "game-premiado", "game-shows", "game-shows-ao-vivo", "halloween", "halloween-da-estrela", "hold-the-dragon-make-a-win", "hold-the-spin", "instantaneos", "jackpots", "jackpots-da-amusnet", "jingle-wins", "jogos-de-dados", "jogos-de-fortuna", "jogos-de-mesa-ao-vivo", "jogos-de-natal", "jogos-do-mes", "jogos-do-mes-live-casino", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogue-com-r1", "lancamentos-jogos-ao-vivo", "lancamentos-ao-vivo", "megaways", "mesas-brasileiras", "milhoes-de-premios-playson", "missao-lucky-rush", "missao-premiada-de-natal", "natal-da-estrela", "novidade-na-estrela", "oriente", "outros-jogos-ao-vivo", "poker", "pragmatic-play", "pragmatic-play-ao-vivo", "recomendados-ao-vivo", "recomendados-pela-estrela-bet", "reino-dos-dragoes", "releases", "spin-wheel", "the-dog-house", "todos-ao-vivo", "torneio", "torneio-delicia-de-pascoa", "torneio-gamzix", "torneio-slots-masters", "video-bingo", "vips", "wazdan"], "type": "string", "description": "Game Type to get screen data.", "name": "gameType", "in": "query", "required": true}], "responses": {"200": {"description": "Successfully", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.LobbyGameGroupShortResponse"}}, "400": {"description": "Invalid query", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Request failed", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/games/get-lobby-group-games-paginated": {"get": {"description": "<p> Get paginated list of games from lobby game group </p>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Games V3"], "summary": "Get Lobby Game Group Games with Pagination", "parameters": [{"type": "string", "default": "pb", "description": "Language code", "name": "language", "in": "query"}, {"type": "boolean", "default": true, "description": "Upcoming games", "name": "upcomingGames", "in": "query"}, {"type": "boolean", "default": false, "description": "Has demo", "name": "hasDemo", "in": "query"}, {"enum": ["populares", "crashgames", "slots", "todos", "jogosdemesa", "no<PERSON><PERSON><PERSON>", "minigames", "jogosaovivo", "originais", "joguecom1", "roletas", "baccarat", "blackjack", "salasbrasile<PERSON><PERSON>", "esportes", "populares-jogos", "top-10-cassino", "top-10-<PERSON><PERSON><PERSON><PERSON><PERSON>", "10-ao-vivo", "animais", "ao-vivo-recomenda-old", "bingo", "bingo-jogos-ao-vivo", "bingo-ao-vivo", "bloco-da-estrela", "cartas", "comprar-bonus", "drop-and-wins", "drop-wins", "em-breve", "especiais-da-estrela", "especial-pascoa", "estrela-indica", "estrela-bet-friday-cashback-e<PERSON><PERSON>", "evolution", "festival-de-premios", "first-person", "first-person-evolution", "fortune-games", "frutas", "futebol", "game-premiado", "game-shows", "game-shows-ao-vivo", "halloween", "halloween-da-estrela", "hold-the-dragon-make-a-win", "hold-the-spin", "instantaneos", "jackpots", "jackpots-da-amusnet", "jingle-wins", "jogos-de-dados", "jogos-de-fortuna", "jogos-de-mesa-ao-vivo", "jogos-de-natal", "jogos-do-mes", "jogos-do-mes-live-casino", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogue-com-r1", "lancamentos-jogos-ao-vivo", "lancamentos-ao-vivo", "megaways", "mesas-brasileiras", "milhoes-de-premios-playson", "missao-lucky-rush", "missao-premiada-de-natal", "natal-da-estrela", "novidade-na-estrela", "oriente", "outros-jogos-ao-vivo", "poker", "pragmatic-play", "pragmatic-play-ao-vivo", "recomendados-ao-vivo", "recomendados-pela-estrela-bet", "reino-dos-dragoes", "releases", "spin-wheel", "the-dog-house", "todos-ao-vivo", "torneio", "torneio-delicia-de-pascoa", "torneio-gamzix", "torneio-slots-masters", "video-bingo", "vips", "wazdan"], "type": "string", "description": "Game Type to get screen data.", "name": "gameType", "in": "query", "required": true}, {"enum": ["all", "arcade", "live", "table-games", "crash-games"], "type": "string", "description": "Game Category Type to get screen data.", "name": "gameCategoryType", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "Page size", "name": "pageSize", "in": "query"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.PaginatedResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.LobbyGameGroupShortResponse"}}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/games/get-real-games": {"get": {"description": "<p> Get games based on a given gameSimbol </p>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Games V3"], "summary": "Get Real Games", "parameters": [{"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}, {"type": "string", "name": "gameSymbol", "in": "query"}, {"type": "string", "name": "language", "in": "query"}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "description": "Current pragmatic user session id", "name": "EB-PS-Session-Id", "in": "header"}], "responses": {"200": {"description": "Successfully login", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.GetRealGamesResponse"}}, "400": {"description": "Invalid query", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Request failed", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/games/search-games": {"get": {"description": "<p> Get search games based on a given gameType </p>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Games V3"], "summary": "Get Search Games", "parameters": [{"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}, {"enum": ["all", "arcade", "live", "table-games", "crash-games", "cassino"], "type": "string", "description": "Game Category Type to get screen data.", "name": "gameCategoryType", "in": "query", "required": true}, {"enum": ["populares", "crashgames", "slots", "todos", "jogosdemesa", "no<PERSON><PERSON><PERSON>", "minigames", "jogosaovivo", "originais", "joguecom1", "roletas", "baccarat", "blackjack", "salasbrasile<PERSON><PERSON>", "esportes", "populares-jogos", "top-10-cassino", "top-10-<PERSON><PERSON><PERSON><PERSON><PERSON>", "10-ao-vivo", "animais", "ao-vivo-recomenda-old", "bingo", "bingo-jogos-ao-vivo", "bingo-ao-vivo", "bloco-da-estrela", "cartas", "comprar-bonus", "drop-and-wins", "drop-wins", "em-breve", "especiais-da-estrela", "especial-pascoa", "estrela-indica", "estrela-bet-friday-cashback-e<PERSON><PERSON>", "evolution", "festival-de-premios", "first-person", "first-person-evolution", "fortune-games", "frutas", "futebol", "game-premiado", "game-shows", "game-shows-ao-vivo", "halloween", "halloween-da-estrela", "hold-the-dragon-make-a-win", "hold-the-spin", "instantaneos", "jackpots", "jackpots-da-amusnet", "jingle-wins", "jogos-de-dados", "jogos-de-fortuna", "jogos-de-mesa-ao-vivo", "jogos-de-natal", "jogos-do-mes", "jogos-do-mes-live-casino", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogue-com-r1", "lancamentos-jogos-ao-vivo", "lancamentos-ao-vivo", "megaways", "mesas-brasileiras", "milhoes-de-premios-playson", "missao-lucky-rush", "missao-premiada-de-natal", "natal-da-estrela", "novidade-na-estrela", "oriente", "outros-jogos-ao-vivo", "poker", "pragmatic-play", "pragmatic-play-ao-vivo", "recomendados-ao-vivo", "recomendados-pela-estrela-bet", "reino-dos-dragoes", "releases", "spin-wheel", "the-dog-house", "todos-ao-vivo", "torneio", "torneio-delicia-de-pascoa", "torneio-gamzix", "torneio-slots-masters", "video-bingo", "vips", "wazdan"], "type": "string", "description": "Game Type to get screen data.", "name": "gameType", "in": "query", "required": true}, {"type": "string", "name": "language", "in": "query"}, {"type": "string", "name": "search", "in": "query", "required": true}], "responses": {"200": {"description": "Successfully", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.GameGroupShortResponse"}}, "400": {"description": "Invalid query", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Request failed", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/home": {"get": {"description": "<p> Handles the request to get home screen information according the required platform (web or mobile) </p>\nError Codes: 10: Internal Server Error | 11: Bad Request | 15: Invalid Session | 58: Third-party Fail", "produces": ["application/json"], "tags": ["Home V3"], "summary": "Home Screen Information", "parameters": [{"enum": ["web", "mobile"], "type": "string", "description": "Platform to get home screen data.", "name": "platform", "in": "query", "required": true}, {"type": "string", "description": "Current user session id", "name": "EB-PS-Session-Id", "in": "header"}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Home screen info for the platform", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.HomeScreenData"}}, "204": {"description": "No home screen info is available for the platform"}, "400": {"description": "Bad request, invalid platform param or session id header not found", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "424": {"description": "Third-party error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/home/<USER>": {"get": {"description": "Returns random top wins for live games, slot games and users", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Home V3"], "summary": "Get Top Wins", "parameters": [{"type": "string", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/login": {"post": {"description": "<p> User login (not used by social media login) </p>\nError Codes: 2: Underage player | 3: Deceased player | 6: Invalid Email | 12: Missing/Invalid fields | 13: Self-excluded gamstop | 14: Invalid Data | 19: Invalid User Profile | 20: Invalid Jurisdiction | 21: Self-exclusion Spelpaus |\n22: Invalid Authentication | 23: Invalid PartnerId | 24: Invalid Grant Type | 26: Invalid Refresh Token | 27: KYC Failed | 28: Missing CRP | 31: Invalid Social Media Type | 32: Already Exists Social Media Id |\n34: User in Negative Category (fraud, closed, suspended) | 35: User does not exists | 36: Invalid password | 37: Invalid screen name | 38: Sel exclusion, cool of | 39: Exceeded login attempts, account disable |\n40: User match gb list | 41: Invalid ECR external id | 42: Already registered social media, need password | 43: Email and social media don't match | 44: User blocked by oasis service | 45: Max login atttempts during cool-of |\n46: Set account to cool-of by max login attempts | 47: Correct login but exceeded attempts 48: Duplicated CNP number | 49: Account closed", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Login V3"], "summary": "User Login", "parameters": [{"description": "User data to login", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.UserLogin"}}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Successfully login", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.LoginResponse"}}, "400": {"description": "Invalid body", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "409": {"description": "Error with user login", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/login/logout": {"post": {"description": "<p> Terminates the current session for the specified player. </p>\nError Codes: 10: Internal server error | 14: Invalid Data | 52: Logout process failed", "produces": ["application/json"], "tags": ["Login V3"], "summary": "Logout request to terminate the current user session", "parameters": [{"type": "string", "description": "Current pragmatic user session id", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Successfully logout", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.LogoutResponse"}}, "400": {"description": "Missing session data", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "422": {"description": "Error in third-party system", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/login/social": {"post": {"description": "<p> Handles the login of a user using social media. </p>\nError Codes: 2: Underage player | 3: Deceased player | 6: Invalid Email | 12: Missing/Invalid fields | 13: Self-excluded gamstop | 14: Invalid Data | 19: Invalid User Profile | 20: Invalid Jurisdiction | 21: Self-exclusion Spelpaus |\n22: Invalid Authentication | 23: Invalid PartnerId | 24: Invalid Grant Type | 26: Invalid Refresh Token | 27: KYC Failed | 28: Missing CRP | 31: Invalid Social Media Type | 32: Already Exists Social Media Id |\n34: User in Negative Category (fraud, closed, suspended) | 35: User does not exists | 36: Invalid password | 37: Invalid screen name | 38: Sel exclusion, cool of | 39: Exceeded login attempts, account disable |\n40: User match gb list | 41: Invalid ECR external id | 42: Already registered social media, need password | 43: Email and social media don't match | 44: User blocked by oasis service | 45: Max login atttempts during cool-of |\n46: Set account to cool-of by max login attempts | 47: Correct login but exceeded attempts 48: Duplicated CNP number | 49: Account closed", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Login V3"], "summary": "User Login by social media", "parameters": [{"description": "Social media user data to login", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.SocialUserLogin"}}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Successfully login", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.LoginResponse"}}, "400": {"description": "Invalid body", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "409": {"description": "Error with social media user login", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/login/social/integration": {"post": {"description": "<p> Handles the login of a user using email and password and integrates social account with the user account. </p>\nError Codes: 2: Underage player | 3: Deceased player | 6: Invalid Email | 12: Missing/Invalid fields | 13: Self-excluded gamstop | 14: Invalid Data | 15: Invalid Session| 19: Invalid User Profile | 20: Invalid Jurisdiction | 21: Self-exclusion Spelpaus |\n22: Invalid Authentication | 23: Invalid PartnerId | 24: Invalid Grant Type | 26: Invalid Refresh Token | 27: KYC Failed | 28: Missing CRP | 31: Invalid Social Media Type | 32: Already Exists Social Media Id |\n34: User in Negative Category (fraud, closed, suspended) | 35: User does not exists | 36: Invalid password | 37: Invalid screen name | 38: Sel exclusion, cool of | 39: Exceeded login attempts, account disable |\n40: User match gb list | 41: Invalid ECR external id | 42: Already registered social media, need password | 43: Email and social media don't match | 44: User blocked by oasis service | 45: Max login atttempts during cool-of |\n46: Set account to cool-of by max login attempts | 47: Correct login but exceeded attempts 48: Duplicated CNP number | 49: Account closed | 50: Request not allowed for partner | 51: Player exists with social media", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Login V3"], "summary": "Login and integrate social account", "parameters": [{"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}, {"description": "Email, password and social media data of the user", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.SocialWithLoginData"}}], "responses": {"200": {"description": "Successfully integrated", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.LoginResponse"}}, "400": {"description": "Invalid body", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "409": {"description": "Error with user login or account integration", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/management/bonusLed": {"post": {"description": "<p>Saves or updates bonus led data</p>\nError Codes: 10: Internal Server Error | 11: Bad Request", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Management V3"], "summary": "Save Bonus Led", "parameters": [{"description": "Bonus Led data", "name": "bonusLed", "in": "body", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.LedBonus"}}}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "The saved bonus led data", "schema": {"type": "array", "items": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.LedBonus"}}}, "400": {"description": "Bad request, invalid bonus led data", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/management/matches": {"post": {"description": "<p>Saves or updates a match</p>\nError Codes: 10: Internal Server Error | 11: Bad Request", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Management V3"], "summary": "Save Match", "parameters": [{"description": "Matches data", "name": "match", "in": "body", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.Match"}}}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "The saved matches", "schema": {"type": "array", "items": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.Match"}}}, "400": {"description": "Bad request, invalid matches data", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/management/sportsBetting": {"post": {"description": "<p>Saves or updates sports betting data</p>\nError Codes: 10: Internal Server Error | 11: Bad Request", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Management V3"], "summary": "Save Sports Betting", "parameters": [{"description": "Sports Betting data", "name": "sportsBetting", "in": "body", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.SportsBettingItem"}}}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "The saved sports betting data", "schema": {"type": "array", "items": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.SportsBettingItem"}}}, "400": {"description": "Bad request, invalid sports betting data", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/management/stories": {"post": {"description": "<p>Saves or updates a story and its banners</p>\nError Codes: 10: Internal Server Error | 11: Bad Request", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Management V3"], "summary": "Save Story", "parameters": [{"description": "Stories data", "name": "story", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.Stories"}}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "The saved story", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.Story"}}, "400": {"description": "Bad request, invalid story data", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/management/top-scorers": {"post": {"description": "<p>Saves or updates a top scorer</p>\nError Codes: 10: Internal Server Error | 11: Bad Request", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Management V3"], "summary": "Save Top Scorer", "parameters": [{"description": "Top Scorers data", "name": "topScorer", "in": "body", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.TopScorer"}}}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "The saved top scorers", "schema": {"type": "array", "items": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.TopScorer"}}}, "400": {"description": "Bad request, invalid top scorers data", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/profile": {"get": {"description": "Get a user's profile with the provided details. If successful, returns the profile and session data.\n<p>Possible error scenarios and corresponding codes:</p>\n<ul>\n<li><b>10</b>: Internal Server Error</li>\n<li><b>11</b>: Bad Request</li>\n</ul>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Profile V3"], "summary": "Get user profile and start a new session", "parameters": [{"type": "string", "default": "swagger", "description": "Identification of the application accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "description": "Session ID for the current user session", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Get Profile successfully", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.GetProfileData"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.GetProfileData"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/profile/balance": {"get": {"description": "Get a balance user's with the provided details. If successful, returns the balance user and session data.\n<p>Possible error scenarios and corresponding codes:</p>\n<ul>\n<li><b>10</b>: Internal Server Error</li>\n<li><b>11</b>: Bad Request</li>\n</ul>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Profile V3"], "summary": "Get balance user and start a new session", "parameters": [{"type": "string", "default": "swagger", "description": "Identification of the application accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "description": "Session ID for the current user session", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Get Profile successfully", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.GetBalanceDetails"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.GetBalanceDetails"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/profile/kyc-details": {"get": {"description": "Get a kyc url of user's with the provided details. If successful, returns the kyc details of user and session data.\n<p>Possible error scenarios and corresponding codes:</p>\n<ul>\n<li><b>10</b>: Internal Server Error</li>\n<li><b>11</b>: Bad Request</li>\n</ul>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Profile V3"], "summary": "Get kyc details user and start a new session", "parameters": [{"type": "string", "default": "swagger", "description": "Identification of the application accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "description": "Session ID for the current user session", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Get user kyc details successfully", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.KycResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.KycResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/profile/kyc-url": {"get": {"description": "Get a kyc url of user's with the provided details. If successful, returns the kyc url of user and session data.\n<p>Possible error scenarios and corresponding codes:</p>\n<ul>\n<li><b>10</b>: Internal Server Error</li>\n<li><b>11</b>: Bad Request</li>\n</ul>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Profile V3"], "summary": "Get kyc url user and start a new session", "parameters": [{"type": "string", "default": "swagger", "description": "Identification of the application accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "description": "Session ID for the current user session", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Get user kyc url successfully", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.UserThirdPartyKYCUrl"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.UserThirdPartyKYCUrl"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/profile/last-played-games": {"get": {"description": "<p>Get for referral eligible data.</p>\nError Codes: 10: Internal Server Error", "produces": ["application/json"], "tags": ["Profile V3"], "summary": "Get for Referral eligible data", "parameters": [{"type": "string", "description": "Current pragmatic user session id", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}, {"type": "string", "default": "5", "description": "Limit", "name": "limit", "in": "query", "required": true}], "responses": {"200": {"description": "Last played games", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.LastPlayedGamesResponse"}}, "204": {"description": "No bonus is available for the platform"}, "400": {"description": "Bad request, invalid platform param", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/profile/send-auth-code": {"post": {"description": "Send auth code. If successful, returns the send auth code ok.\n<p>Possible error scenarios and corresponding codes:</p>\n<ul>\n<li><b>10</b>: Internal Server Error</li>\n<li><b>11</b>: Bad Request</li>\n<li><b>12</b>: Missing or Invalid Fields</li>\n<li><b>13</b>: Self-Excluded (GamStop)</li>\n<li><b>14</b>: Invalid Data</li>\n<li><b>15</b>: Invalid Session</li>\n</ul>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Profile V3"], "summary": "Send auth code", "parameters": [{"type": "string", "default": "swagger", "description": "Identification of the application accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "description": "Session ID for the current user session", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Auth Code successfully sent", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.SendAuthCodeResponse"}}, "400": {"description": "Invalid request body", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "409": {"description": "Auth Code data not accepted", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/profile/third-party-kyc": {"post": {"description": "Get a kyc url of user's with the provided details. If successful, returns the kyc details of user and session data.\n<p>Possible error scenarios and corresponding codes:</p>\n<ul>\n<li><b>10</b>: Internal Server Error</li>\n<li><b>11</b>: Bad Request</li>\n</ul>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Profile V3"], "summary": "Get kyc details user and start a new session", "parameters": [{"description": "ThirdPartyKYCUrl data", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.ThirdPartyKYCUrl"}}, {"type": "string", "default": "swagger", "description": "Identification of the application accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "description": "Session ID for the current user session", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "ThirdParty successfully", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.UpdateProfileResponse"}}, "400": {"description": "Invalid request body", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "409": {"description": "ThirdParty data not accepted", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/profile/update-profile": {"post": {"description": "Update a user's profile with the provided details. If successful, returns the updated profile and session data.\n<p>Possible error scenarios and corresponding codes:</p>\n<ul>\n<li><b>10</b>: Internal Server Error</li>\n<li><b>11</b>: Bad Request</li>\n<li><b>12</b>: Missing or Invalid Fields</li>\n<li><b>13</b>: Self-Excluded (GamStop)</li>\n<li><b>14</b>: Invalid Data</li>\n<li><b>15</b>: Invalid Session</li>\n<li><b>16</b>: Duplicated ScreenName</li>\n<li><b>17</b>: Missing Handler</li>\n<li><b>18</b>: Invalid Provider Connection</li>\n<li><b>19</b>: Invalid User Profile</li>\n<li><b>20</b>: Invalid Jurisdiction</li>\n<li><b>21</b>: Self-Exclusion (Spelpaus)</li>\n<li><b>22</b>: Invalid Authentication</li>\n<li><b>23</b>: Invalid PartnerId</li>\n<li><b>24</b>: Invalid Grant Type</li>\n<li><b>25</b>: Missing PartnerId</li>\n<li><b>26</b>: Invalid Refresh Token</li>\n<li><b>27</b>: KYC Failed</li>\n<li><b>28</b>: Missing CRP</li>\n<li><b>29</b>: Duplicated CRP</li>\n<li><b>30</b>: Invalid Date of Birth</li>\n<li><b>31</b>: Invalid Social Media Type</li>\n<li><b>32</b>: Already Exists Social Media ID</li>\n<li><b>33</b>: Invalid Document Data</li>\n<li><b>58</b>: Third-Party Failure</li>\n</ul>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Profile V3"], "summary": "Update user profile and start a new session", "parameters": [{"description": "User profile data", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.UpdateProfile"}}, {"type": "string", "default": "swagger", "description": "Identification of the application accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "description": "Session ID for the current user session", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Profile successfully updated", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.UpdateProfileResponse"}}, "400": {"description": "Invalid request body", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "409": {"description": "Profile data not accepted", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/profile/verify-auth-code": {"post": {"description": "Verify auth code. If successful, returns the verify auth code.\n<p>Possible error scenarios and corresponding codes:</p>\n<ul>\n<li><b>10</b>: Internal Server Error</li>\n<li><b>11</b>: Bad Request</li>\n<li><b>12</b>: Missing or Invalid Fields</li>\n<li><b>13</b>: Self-Excluded (GamStop)</li>\n<li><b>14</b>: Invalid Data</li>\n<li><b>15</b>: Invalid Session</li>\n</ul>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Profile V3"], "summary": "Verify auth code", "parameters": [{"description": "Verify auth code data", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.VerifyAuthCode"}}, {"type": "string", "default": "swagger", "description": "Identification of the application accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "description": "Session ID for the current user session", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Auth Code successfully sent", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.SendAuthCodeResponse"}}, "400": {"description": "Invalid request body", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "409": {"description": "Auth Code data not accepted", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/promotions": {"get": {"description": "Retrieves a list of promotions with optional filtering by language, affiliate, and visibility.\nThis endpoint integrates with the Pragmatic Solutions Front-Office API to fetch promotion data.\nSupports caching for improved performance - cached responses are served for non-authenticated requests.\nSession-based requests bypass cache to ensure fresh data for authenticated users.\n\n**Query Parameters:**\n- `languageId`: 2-character ISO 639-1 language code (e.g., \"en\", \"pt\", \"es\"). Defaults to \"en\" if not provided.\n- `affiliateId`: Optional affiliate identifier for filtering promotions specific to an affiliate partner.\n- `hiddenPromotion`: Boolean flag to include/exclude hidden promotions. Defaults to false (hidden promotions excluded).\n\n**Authentication:**\n- Session ID can be provided via `EB-PS-Session-Id` header for authenticated requests\n- Authenticated requests bypass cache and return fresh data\n- Non-authenticated requests use cached data when available\n\n**Error Codes:**\n- 10: Internal Server Error\n- 11: Bad Request (invalid parameters)\n- 15: Invalid Session\n- 58: Third-party Fail (PGS API error)\n- 62: Promotion Service Unavailable\n- 64: Invalid Promotion Parameters\n- 67: Rate Limit Exceeded", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Promotions V3"], "summary": "Get Promotions", "parameters": [{"type": "string", "default": "pb", "example": "pb", "description": "Language ID (ISO 639-1 format, 2 characters)", "name": "languageId", "in": "query"}, {"type": "string", "example": "Test123", "description": "Affiliate ID for filtering promotions", "name": "affiliateId", "in": "query"}, {"type": "boolean", "default": false, "example": false, "description": "Include hidden promotions in results", "name": "hiddenPromotion", "in": "query"}, {"type": "string", "description": "Current user session id for authenticated requests", "name": "EB-PS-Session-Id", "in": "header"}, {"type": "string", "default": "swagger", "description": "Identification of the application", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key for API authentication", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Request timestamp for signature validation", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "Unique nonce for request security", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC signature for request validation", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Successfully retrieved list of promotions", "schema": {"type": "array", "items": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.Promotion"}}}, "204": {"description": "No promotions found for the given criteria", "schema": {"type": "string"}}, "400": {"description": "Bad request - invalid query parameters (e.g., invalid languageId format)", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "401": {"description": "Unauthorized - invalid or expired session", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "424": {"description": "Failed dependency - PGS Front-Office API error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "429": {"description": "Too many requests - rate limit exceeded", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error - unexpected application error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "503": {"description": "Service unavailable - promotion service temporarily unavailable", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/promotions/{promotionId}": {"get": {"description": "Retrieves detailed information for a specific promotion by its ID with optional filtering by language, affiliate, and visibility.\nThis endpoint integrates with the Pragmatic Solutions Front-Office API to fetch detailed promotion data.\nSupports caching for improved performance - cached responses are served for non-authenticated requests.\nSession-based requests bypass cache to ensure fresh data for authenticated users.\n\n**Path Parameters:**\n- `promotionId`: Unique identifier of the promotion to retrieve details for (required)\n\n**Query Parameters:**\n- `languageId`: 2-character ISO 639-1 language code (e.g., \"en\", \"pt\", \"es\"). Defaults to \"pb\" if not provided.\n- `affiliateId`: Optional affiliate identifier for filtering promotions specific to an affiliate partner.\n- `hiddenPromotion`: Boolean flag to include/exclude hidden promotions. Defaults to false (hidden promotions excluded).\n\n**Authentication:**\n- Session ID can be provided via `EB-PS-Session-Id` header for authenticated requests\n- Authenticated requests bypass cache and return fresh data\n- Non-authenticated requests use cached data when available\n\n**Error Codes:**\n- 10: Internal Server Error\n- 11: Bad Request (invalid parameters)\n- 15: Invalid Session\n- 58: Third-party Fail (PGS API error)\n- 62: Promotion Service Unavailable\n- 63: Promotion Not Found\n- 64: Invalid Promotion Parameters\n- 67: Rate Limit Exceeded", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Promotions V3"], "summary": "Get Promotion Details", "parameters": [{"type": "string", "example": "123", "description": "Promotion ID (required)", "name": "promotionId", "in": "path", "required": true}, {"type": "string", "default": "pb", "example": "pb", "description": "Language ID (ISO 639-1 format, 2 characters)", "name": "languageId", "in": "query"}, {"type": "string", "example": "Test123", "description": "Affiliate ID for filtering promotions", "name": "affiliateId", "in": "query"}, {"type": "boolean", "default": false, "example": false, "description": "Include hidden promotions in results", "name": "hiddenPromotion", "in": "query"}, {"type": "string", "description": "Current user session id for authenticated requests", "name": "EB-PS-Session-Id", "in": "header"}, {"type": "string", "default": "swagger", "description": "Identification of the application", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key for API authentication", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Request timestamp for signature validation", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "Unique nonce for request security", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC signature for request validation", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Successfully retrieved promotion details", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.Promotion"}}, "400": {"description": "Bad request - invalid parameters (e.g., invalid promotionId or languageId format)", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "401": {"description": "Unauthorized - invalid or expired session", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "404": {"description": "Not found - promotion not found or hidden promotion access denied", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "424": {"description": "Failed dependency - PGS Front-Office API error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "429": {"description": "Too many requests - rate limit exceeded", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error - unexpected application error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "503": {"description": "Service unavailable - promotion service temporarily unavailable", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/reviews": {"get": {"description": "<p>Retrieves app reviews with filtering, sorting, and pagination</p>\nError Codes: 10: Internal Server Error", "produces": ["application/json"], "tags": ["Reviews V3"], "summary": "Get App Reviews", "parameters": [{"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}, {"type": "string", "description": "Start date for filtering (RFC3339 format)", "name": "start_date", "in": "query"}, {"type": "string", "description": "End date for filtering (RFC3339 format)", "name": "end_date", "in": "query"}, {"enum": ["apple", "google"], "type": "string", "description": "Filter by store type (apple or google)", "name": "store_type", "in": "query"}, {"maximum": 5, "minimum": 1, "type": "integer", "description": "Minimum rating (1-5)", "name": "min_rating", "in": "query"}, {"maximum": 5, "minimum": 1, "type": "integer", "description": "Maximum rating (1-5)", "name": "max_rating", "in": "query"}, {"type": "string", "description": "Search term for content or author name", "name": "search", "in": "query"}, {"minimum": 1, "type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"maximum": 100, "minimum": 1, "type": "integer", "default": 20, "description": "Page size", "name": "page_size", "in": "query"}, {"type": "boolean", "default": true, "description": "Sort by created_at descending", "name": "sort_desc", "in": "query"}], "responses": {"200": {"description": "Paginated reviews with summary", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.ReviewPage"}}, "400": {"description": "Bad request, invalid parameters", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}, "post": {"description": "<p>Saves a new app review from Apple App Store or Google Play</p>\nError Codes: 10: Internal Server Error | 11: Bad Request", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Reviews V3"], "summary": "Save App Review", "parameters": [{"description": "Review data", "name": "review", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.CreateReviewRequest"}}, {"type": "string", "description": "Session ID for the current user session", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"201": {"description": "Review successfully saved", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.SuccessResponse"}}, "400": {"description": "Bad request, invalid review data", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/reviews/export": {"get": {"description": "<p>Exports app reviews as a CSV file with filtering and sorting</p>\nError Codes: 10: Internal Server Error", "produces": ["text/csv"], "tags": ["Reviews V3"], "summary": "Export App Reviews as CSV", "parameters": [{"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}, {"type": "string", "description": "Start date for filtering (RFC3339 format)", "name": "start_date", "in": "query"}, {"type": "string", "description": "End date for filtering (RFC3339 format)", "name": "end_date", "in": "query"}, {"enum": ["apple", "google"], "type": "string", "description": "Filter by store type (apple or google)", "name": "store_type", "in": "query"}, {"maximum": 5, "minimum": 1, "type": "integer", "description": "Minimum rating (1-5)", "name": "min_rating", "in": "query"}, {"maximum": 5, "minimum": 1, "type": "integer", "description": "Maximum rating (1-5)", "name": "max_rating", "in": "query"}, {"type": "string", "description": "Search term for content or author name", "name": "search", "in": "query"}, {"type": "boolean", "default": true, "description": "Sort by created_at descending", "name": "sort_desc", "in": "query"}], "responses": {"200": {"description": "CSV file with reviews", "schema": {"type": "file"}}, "400": {"description": "Bad request, invalid parameters", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/stories": {"get": {"description": "<p>Retrieves stories filtered by type (casino/sportsbook)</p>\nError Codes: 10: Internal Server Error", "produces": ["application/json"], "tags": ["Stories V3"], "summary": "Get Stories", "parameters": [{"enum": ["casino", "sportsbook"], "type": "string", "description": "Story type", "name": "type", "in": "query", "required": true}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "List of stories of the specified type", "schema": {"type": "array", "items": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.Story"}}}, "204": {"description": "No stories found for the specified type"}, "400": {"description": "Bad request, invalid type parameter", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/utils": {"post": {"description": "<p> Register a new user with the provided details and start a session for the user (login).\nThe session data is returned. If an error occurs during the login process, http status 201 is returned.</p>\nError Codes: 5: <PERSON><PERSON><PERSON> registered CPF | 7: Duplicated Email | 10: Internal Server Error | 11: Bad request | 12: Missing/Invalid fields |13: Self-excluded gamstop | 14: Invalid Data | 15: InvalidSession\n16: Duplicated ScreenName | 17: Missing Handler | 18: Invalid Provider Connection | 19: Invalid User Profile | 20: Invalid Jurisdiction | 21: Self-exclusion Spelpaus | 22: Invalid Authentication\n23: Invalid PartnerId | 24: Invalid Grant Type | 25: Missin PartnerId | 26: Invalid Refresh Token | 27: KYC Failed | 28: Missing CRP | 29: Duplicated CRP | 30: Invalid Date of Birth | 31: Invalid Social Media Type\n32: Already Exists Social Media Id | 33: Invalid Document Data | 58: Third-party Fail", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Registration V3"], "summary": "Register a new user starting a new session for the user", "parameters": [{"description": "User details", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.User"}}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Successfully registered user and logged in", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.LoginResponse"}}, "201": {"description": "Successfully registered user but not logged in", "schema": {"$ref": "#/definitions/gin.H"}}, "400": {"description": "Invalid body", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "409": {"description": "User data not accepted", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "424": {"description": "Third-party error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/utils/active-bonus": {"get": {"description": "<p>Active bonus response.</p>\nError Codes: 10: Internal Server Error", "produces": ["application/json"], "tags": ["Registration V3"], "summary": "Get active bonus response", "parameters": [{"type": "string", "description": "Current pragmatic user session id", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Bonus response", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.BonusResponse"}}, "400": {"description": "Bad request, invalid platform param", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/utils/address": {"get": {"description": "Make request à API ViaCEP e return all data.", "produces": ["application/json"], "tags": ["Registration V3"], "summary": "find address by CEP brazil.", "parameters": [{"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}, {"type": "string", "description": "CEP no format 00000000 (numerics)", "name": "cep", "in": "header", "required": true}], "responses": {"200": {"description": "Return address data", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.CepResponse"}}, "400": {"description": "Erro de validação do CEP", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "Erro interno ao processar a requisição", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/v3/utils/bonus": {"get": {"description": "<p>Get bonus available during user utils with expiration time.</p>\nError Codes: 10: Internal Server Error", "produces": ["application/json"], "tags": ["Registration V3"], "summary": "Get bonus available during user utils", "parameters": [{"enum": ["web", "mobile"], "type": "string", "description": "Platform for the bonus.", "name": "platform", "in": "query", "required": true}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Bonus data", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_adapters_inbound_api_dto.Bonus"}}, "204": {"description": "No bonus is available for the platform"}, "400": {"description": "Bad request, invalid platform param", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/utils/claim-bonus": {"get": {"description": "<p>Claim or reject bonus response.</p>\nError Codes: 10: Internal Server Error", "produces": ["application/json"], "tags": ["Registration V3"], "summary": "Get for claim or reject bonus response", "parameters": [{"type": "boolean", "name": "claimBonus", "in": "query"}, {"type": "string", "name": "ecrBonusId", "in": "query"}, {"type": "string", "description": "Current pragmatic user session id", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Claim or reject bonus response", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.ClaimOrRejectBonusResponse"}}, "400": {"description": "Bad request, invalid platform param", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/utils/client/{cpf}": {"get": {"description": "<p>Retrieves detailed client information using a CPF from Pay2Free service.</p>\nError Codes: 10: Internal Server Error | 11: Bad request | 404: Not Found | 424: Third-party error", "produces": ["application/json"], "tags": ["Registration V3"], "summary": "Get client information by CPF", "parameters": [{"type": "string", "description": "CPF number as a string with 11 numeric digits", "name": "cpf", "in": "path", "required": true}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Client information retrieved successfully", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.Pay2FreeCpfKycDetails"}}, "400": {"description": "Invalid CPF format", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "404": {"description": "Client not found", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "424": {"description": "Third-party error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/utils/drop-bonus": {"get": {"description": "<p>Claim or reject bonus response.</p>\nError Codes: 10: Internal Server Error", "produces": ["application/json"], "tags": ["Registration V3"], "summary": "Get for claim or reject bonus response", "parameters": [{"type": "string", "name": "ecrBonusId", "in": "query"}, {"type": "string", "description": "Current pragmatic user session id", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Drop bonus response", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.DropBonusResponse"}}, "400": {"description": "Bad request, invalid platform param", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/utils/phoneavailability": {"post": {"description": "<p>Check if phone number is available to be register, checking for duplicates.</p>\nError Codes: 8: Already registered phone | 10: Internal Server error | 11: Bad request | 14: Invalid Data", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Registration V3"], "summary": "Check phone number availability", "parameters": [{"description": "Isd code and phone number as string", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_adapters_inbound_api_dto.CheckPhoneAvailability"}}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Available phone number", "schema": {"$ref": "#/definitions/gin.H"}}, "400": {"description": "Invalid request body or isd/number of the phone", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "409": {"description": "Phone already registered", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "422": {"description": "Error processing data", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/utils/referral-eligible": {"get": {"description": "<p>Get for referral eligible data.</p>\nError Codes: 10: Internal Server Error", "produces": ["application/json"], "tags": ["Registration V3"], "summary": "Get for Referral eligible data", "parameters": [{"type": "string", "description": "Current pragmatic user session id", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Referral eligible data", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.ReferralEligibleResponse"}}, "204": {"description": "No bonus is available for the platform"}, "400": {"description": "Bad request, invalid platform param", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/utils/social": {"post": {"description": "<p>Register a new user with the provided details and social media data, and start a session for the user (login).\nThe session data is returned. If an error occurs during the login process, http status 201 is returned.</p>\nError Codes: 5: Alreade registered CPF | 7: Duplicated Email | 10: Internal Server Error | 11: Bad request | 12: Missing/Invalid fields |13: Self-excluded gamstop | 14: Invalid Data | 15: InvalidSession\n16: Duplicated <PERSON>Name | 17: Missing Handler | 18: Invalid Provider Connection | 19: Invalid User Profile | 20: Invalid Jurisdiction | 21: Self-exclusion Spelpaus | 22: Invalid Authentication\n23: Invalid PartnerId | 24: Invalid Grant Type | 25: Missin PartnerId | 26: Invalid Refresh Token | 27: KYC Failed | 28: Missing CRP | 29: Duplicated CRP | 30: Invalid Date of Birth | 31: Invalid Social Media Type\n32: Already Exists Social Media Id | 33: Invalid Document Data | 58: Third-party Fail", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Registration V3"], "summary": "Register a new user using social media starting a new session for the user", "parameters": [{"description": "User details with social media specification", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.SocialMediaUser"}}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Successfully registered user", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.LoginResponse"}}, "201": {"description": "Successfully registered user but not logged in", "schema": {"$ref": "#/definitions/gin.H"}}, "400": {"description": "Invalid body", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "409": {"description": "User data not accepted", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "424": {"description": "Third-party error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/utils/user-acknowledgement": {"get": {"description": "<p>Get for update player acknowledgement.</p>\nError Codes: 10: Internal Server Error", "produces": ["application/json"], "tags": ["Registration V3"], "summary": "Get for update player acknowledgement available during user registration", "parameters": [{"type": "string", "description": "Current pragmatic user session id", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "User acknowledgement data", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.UpdatePlayerDetailsAcknowledgementResponse"}}, "204": {"description": "No bonus is available for the platform"}, "400": {"description": "Bad request, invalid platform param", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/utils/user-policy-details": {"post": {"description": "<p> Update policy of user with the provided details </p>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Registration V3"], "summary": "Update policy of user", "parameters": [{"description": "PolicyUser details", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.PolicyUser"}}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "description": "Session ID for the current user session", "name": "EB-PS-Session-Id", "in": "header", "required": true}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Successfully registered user", "schema": {"$ref": "#/definitions/gin.H"}}, "400": {"description": "Invalid body", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "409": {"description": "PolicyUser data not accepted", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "424": {"description": "Third-party error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/utils/validatecpf": {"post": {"description": "<p>Validates CPF for user utils, checking for duplicates,\ninvalid CPF, underage CPF, canceled CPF and in accordance with interal policies.</p>\nError Codes: 1: Invalid CPF | 2: Underage CPF | 3: Deceased CPF | 4: Internal policies | 5: Duplicated | 9: Technical Error | 10: Internal Server Error | 11: Bad request", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Registration V3"], "summary": "Validate a CPF", "parameters": [{"description": "Cpf number as a string with 11 numeric digits", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_adapters_inbound_api_dto.CpfValidation"}}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Valid CPF", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_core_domain.CpfPreview"}}, "400": {"description": "Invalid request body or CPF", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "404": {"description": "Invalid CPF", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "409": {"description": "CPF already registered", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "422": {"description": "CPF invalidations due to underage, deceased or internal policies", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "424": {"description": "Third-party error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}, "/api/v3/utils/validateemail": {"post": {"description": "<p>Validates email for user utils, checking for duplicates or invalid email.</p>\nError Codes: 6: Invalid Email | 7: Duplicated Email | 9: Technical Error | 10: Internal Server error | 11: Bad request", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Registration V3"], "summary": "Validate a Email", "parameters": [{"description": "Email as a string", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_internal_adapters_inbound_api_dto.EmailValidation"}}, {"type": "string", "default": "swagger", "description": "Identification of the application that is accessing the API", "name": "EB-Client-App", "in": "header"}, {"type": "string", "default": "", "description": "Application key", "name": "X-API-Key", "in": "header"}, {"type": "string", "default": "", "description": "Timestamp", "name": "X-Timestamp", "in": "header"}, {"type": "string", "default": "", "description": "<PERSON><PERSON>", "name": "X-<PERSON><PERSON>", "in": "header"}, {"type": "string", "default": "", "description": "HMAC Signature", "name": "X-Signature", "in": "header"}], "responses": {"200": {"description": "Valid email", "schema": {"$ref": "#/definitions/gin.H"}}, "400": {"description": "Invalid request body or non-existent email ", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "404": {"description": "<PERSON><PERSON><PERSON>", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "409": {"description": "Email already registered", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_EstrelaBet_platform-api_pkg_shared.ErrorResponse"}}}}}}}