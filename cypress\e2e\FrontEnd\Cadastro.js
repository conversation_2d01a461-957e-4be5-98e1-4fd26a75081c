/// <reference types="cypress" />

import loc from '../../support/locators'
import { Utility } from "../../support/utility"
import '@cypress/grep'
import { blockTrackers } from '../../support/blockTrackers'

const url = new Utility().getBaseUrl();
const urlRequest = new Utility().getApiUrl();
const urlBff = new Utility().getApiBffUrl();

describe('Cadastro', () => {
    it.only('Deve validar elementos no cadastro via botão do header', () => {
        blockTrackers();
        cy.visit(url)

        cy.log('Acessando cadastro via botão do header')
        cy.contains('button', 'Cadastrar')
        cy.get(loc.CADASTRO.BOTAO_CADASTRAR_HEADER)
            .click()

        cy.validateRegistrationPageElements()
    })
    it('Deve validar elementos no cadastro via botão da home', () => {
        blockTrackers();
        cy.visit(url)

        cy.log('Acessando cadastro via botão da home')
        cy.isVisible('button', 'Cadastre-se')
        cy.get(loc.CADASTRO.BOTAO_CADASTRAR_HOME)
            .contains('Cadastre-se')
            .should('be.visible')
            .click()

        cy.validateRegistrationPageElements()
    })
    it('Deve retornar 400 quando CPF já está cadastrado', () => {
        cy.intercept('POST', '**/validate/cpf').as('validateCpf')
        cy.api({
            method: 'POST',
            url: urlBff + '/validate/cpf',
            body: {
                cpf: "11983538647"
            },
            failOnStatusCode: false
        }).then((response) => {
            expect(response.status).to.eq(400)
            expect(response.body).to.have.property('code', '100762')
            expect(response.body).to.have.property('message', 'CPF já está cadastrado.')
        })
    })
    it('Deve retornar 200 e os dados mascarados do usuário', () => {
        cy.api({
            method: 'POST',
            url: urlBff + '/v2/registration/validate/cpf',
            body: {
                cpf: "53655184972"
            }
        }).then((response) => {
            expect(response.status).to.eq(200)
            expect(response.body).to.have.property('data')
            expect(response.body.data).to.have.property('cpf', '53655184972')
            expect(response.body.data).to.have.property('maskedName').and.to.match(/^\w+/)
            expect(response.body.data).to.have.property('birthDate').and.to.not.be.empty
        })
    })
    it('Deve retornar 422 e mensagem de documento inválido', () => {
        cy.api({
            method: 'POST',
            url: urlBff + '/v2/registration/validate/cpf',
            failOnStatusCode: false,
            body: {
                cpf: "80807788090"
            }
        }).then((response) => {
            expect(response.status).to.eq(422)
            expect(response.body).to.have.property('code', 'INVALID_DOCUMENT')
            expect(response.body).to.have.property('message', 'O documento solicitado não está em um formato válido.')
        })
    })
    it('Deve retornar 400 e mensagem ao enviar e-mail inválido', () => {
        cy.api({
            method: 'POST',
            url: urlBff + '/validate/email',
            failOnStatusCode: false,
            body: {
                email: 'teste'
            }
        }).then(({ status, body, headers }) => {
            expect(status).to.eq(400)
            expect(body).to.have.property('code', '100164')
            expect(body).to.have.property('message').and.match(/Dados inválidos/i)
            expect(headers).to.have.property('content-type').and.match(/application\/json/i)
        })
    })
    it('Deve retornar que o e-mail já está cadastrado', () => {
        cy.api({
            method: 'POST',
            url: urlBff + '/validate/email',
            body: {
                email: "<EMAIL>"
            }
        }).then((response) => {
            expect(response.status).to.eq(200)
            expect(response.body.data.isUnique).to.be.false
        })
    })
    it('Deve validar número de telefone com sucesso', () => {
        cy.api({
            method: 'POST',
            url: urlBff + '/validate/telephoneNumber',
            body: {
                isdCode: "55",
                telephoneNumber: "41121321231"
            }
        }).then((response) => {
            expect(response.status).to.eq(200)
            expect(response.body.data.isValid).to.be.true
        })
    })
    it('Deve retornar 400 quando o número de telefone já está cadastrado', () => {
        cy.api({
            method: 'POST',
            url: urlBff + '/validate/telephoneNumber',
            failOnStatusCode: false,
            body: {
                isdCode: "55",
                telephoneNumber: "11111111111"
            }
        }).then(({ status, body }) => {
            expect(status).to.eq(400)
            expect(body).to.have.property('code', '100294')
            expect(body).to.have.property('message', 'Esse número de celular já está cadastrado.')
        })
    })
    it('Deve retornar 400 quando o número de telefone não é válido', () => {
        cy.api({
            method: 'POST',
            url: urlBff + '/validate/telephoneNumber',
            failOnStatusCode: false,
            body: {
                isdCode: "55",
                telephoneNumber: "teste"
            }
        }).then(({ status, body }) => {
            expect(status).to.eq(400)
            expect(body).to.have.property('code', '100164')
            expect(body).to.have.property('message', 'Invalid Brazilian telephone number')
        })
    })
    it('Deve retornar 400 quando o codigo internacional não é válido', () => {
        cy.api({
            method: 'POST',
            url: urlBff + '/validate/telephoneNumber',
            failOnStatusCode: false,
            body: {
                isdCode: "XX",
                telephoneNumber: "teste"
            }
        }).then(({ status, body }) => {
            expect(status).to.eq(400)
            expect(body).to.have.property('code', '100164')
            expect(body).to.have.property('message', 'Invalid international telephone number')
        })
    })
    //it('Cadastrar usuario', () => { }) 
})