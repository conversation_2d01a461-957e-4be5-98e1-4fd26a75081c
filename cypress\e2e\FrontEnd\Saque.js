/// <reference types="cypress" />

import loc from '../../support/locators'
import { Utility } from "../../support/utility"
import '@cypress/grep'
import { blockTrackers } from '../../support/blockTrackers'

const urlBff = new Utility().getApiBffUrl();

describe('Cadastro', () => {
    beforeEach(() => {
        blockTrackers();
        cy.loginToAuth0IdSession(Cypress.env('user_name'), Cypress.env('user_password'))
    });
    it('Deve validar os elementos da tela de saque', () => {
        cy.get('.nebulosa-header__desktopButtonWrapper > .nebulosa-avatar__avatarContainer > .nebulosa-avatar__root > .nebulosa-avatar__fallback')
            .should('be.visible')
            .click();

        cy.isVisible('button', 'Sacar')
        cy.clickByContains('button', 'Sacar')

        // Valida título principal
        cy.isVisible('div', 'Saque')

        // Valida aviso de atenção
        cy.isVisible('div', 'Fique atento!')

        // Valida mensagem com CPF
        cy.isVisible('div', 'O saque será feito via pix utilizando a chave do CPF')
        //.should('contain.text', '081.347.189-30')

        // Valida saldo em carteira
        cy.isVisible('div', 'Saldo em carteira')
        //cy.isVisible('div', 'R$ 1,00')

        // Valida label do input
        cy.isVisible('label', 'Valor do saque')

        // Valida placeholder e valor inicial do input
        cy.get('input[name="amount"]')
            .should('have.attr', 'placeholder', 'Digite o valor a ser sacado')
            .and('have.value', 'R$ 0,00')

        cy.isVisible('button', 'Sacar')
    });
    it('Deve validar o botão de saque', () => {
        cy.get('.nebulosa-header__desktopButtonWrapper > .nebulosa-avatar__avatarContainer > .nebulosa-avatar__root > .nebulosa-avatar__fallback')
            .should('be.visible')
            .click();

        cy.isVisible('button', 'Sacar')
        cy.clickByContains('button', 'Sacar')

        cy.get('input[name="amount"]').type('10')

        cy.isVisible('button', 'Sacar')
        cy.clickMouse('button[type="submit"] span', 'Sacar')

        const sessionId = Cypress.env('session_id');
        cy.api({
            method: 'GET',
            url: urlBff + '/bonus/active',
            failOnStatusCode: false,
            headers: {
                sessionid: `${sessionId}` 
            }
        }).then((response) => {
            if (response.status === 200) {
                cy.log('✅ /bonus/active retornou 200');
            } else {
                cy.log(`⚠️ /bonus/active retornou ${response.status}`);
            }
            cy.log(`📦 Body: ${JSON.stringify(response.body).substring(0, 300)}...`);
            expect(response.status).to.be.oneOf([200, 400]); 
        });
        cy.api({
            method: 'GET',
            url: urlBff + '/cashier/instruments/details',
            failOnStatusCode: false,
            headers: {
                sessionid: `${sessionId}` 
            }
        }).then((response) => {
            if (response.status === 200) {
                cy.log('✅ /cashier/instruments/details retornou 200');
            } else {
                cy.log(`⚠️ /cashier/instruments/details retornou ${response.status}`);
            }

            cy.log(`📦 Body: ${JSON.stringify(response.body).substring(0, 300)}...`);
            expect(response.status).to.be.oneOf([200, 400]);
        });
        const payload = {
            depositAmount: 1,
            paymentMethod: "PIXP2F",
            instrumentId: 6654566,
            //lnSessionId: sessionId, e outro idsession
        }
        cy.api({
            method: 'POST',
            url: urlBff + '/cashier/wallet-withdrawal',
            body: payload,
            failOnStatusCode: false
        }).then((response) => {
            if (response.status === 200) {
                cy.log('✅ Requisição bem-sucedida: ' + response.status)
            } else if (response.status === 400) {
                cy.log('⚠️ Requisição inválida: ' + response.status)
            } else {
                cy.log('ℹ️ Outro status: ' + response.status)
            }
        })
        cy.api({
            method: 'GET',
            url: 'https://api-livenessv2.pay2freetech.com/liveness/validate-token',
            failOnStatusCode: false,
        }).then((response) => {
            if (response.status === 200) {
                cy.log('✅ liveness/validate-token retornou 200');
            } else {
                cy.log(`⚠️ liveness/validate-token retornou ${response.status}`);
            }
            cy.log(`📦 Body: ${JSON.stringify(response.body).substring(0, 300)}...`)
        });
         cy.api({
            method: 'GET',
            url: 'https://api-livenessv2.pay2freetech.com/liveness/settings',
            failOnStatusCode: false,
        }).then((response) => {
            if (response.status === 200) {
                cy.log('✅ liveness/validate-token retornou 200');
            } else {
                cy.log(`⚠️ liveness/validate-token retornou ${response.status}`);
            }
            cy.log(`📦 Body: ${JSON.stringify(response.body).substring(0, 300)}...`)
        });
    });
});