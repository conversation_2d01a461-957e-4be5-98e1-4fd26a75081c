<!doctype html>
<html lang="en"><head><meta charSet="utf-8"/><meta http-equiv="X-UA-Compatible" content="IE=edge"/><meta name="viewport" content="width=device-width, initial-scale=1"/><title>Mochawesome Report</title><link rel="stylesheet" href="assets\app.css"/></head><body data-raw="{&quot;stats&quot;:{&quot;suites&quot;:11,&quot;tests&quot;:81,&quot;passes&quot;:8,&quot;pending&quot;:66,&quot;failures&quot;:6,&quot;testsRegistered&quot;:81,&quot;passPercent&quot;:9.876543209876543,&quot;pendingPercent&quot;:81.48148148148148,&quot;other&quot;:0,&quot;hasOther&quot;:false,&quot;skipped&quot;:1,&quot;hasSkipped&quot;:true,&quot;start&quot;:&quot;2025-09-05T19:23:33.444Z&quot;,&quot;end&quot;:&quot;2025-09-05T19:29:14.535Z&quot;,&quot;duration&quot;:341091},&quot;results&quot;:[{&quot;uuid&quot;:&quot;fd615694-fae5-433f-ba82-a1520a8df0f3&quot;,&quot;title&quot;:&quot;&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\Backend\\api-plataform.cy.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\Backend\\api-plataform.cy.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[],&quot;suites&quot;:[{&quot;uuid&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;title&quot;:&quot;Platform API - smoke 417&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\Backend\\api-plataform.cy.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\Backend\\api-plataform.cy.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;GET /cashier/deposit/account -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /cashier/deposit/account -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/cashier/deposit/account&#x27;),\n  headers: withSession(commonHeaders()),\n  qs: {\n    deposit: &#x27;&#x27;,\n    verified: true\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;a7d762cd-a13a-4913-8d76-aed399385178&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /cashier/deposit/queued-notifications -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /cashier/deposit/queued-notifications -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/cashier/deposit/queued-notifications&#x27;),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;9d97d1cf-b2bf-46f6-aba3-ff5246be3d7a&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;DELETE /cashier/deposit/queued-notifications -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 DELETE /cashier/deposit/queued-notifications -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;DELETE&#x27;,\n  url: api(&#x27;/cashier/deposit/queued-notifications&#x27;),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;2317aefa-a985-4dfd-97d3-6dd7a4ee0d90&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /cashier/deposit/screen-info -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /cashier/deposit/screen-info -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/cashier/deposit/screen-info&#x27;),\n  headers: withSession(commonHeaders()),\n  qs: {\n    platform: &#x27;web&#x27;\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;2fe5048e-0639-462f-b31d-6466d6cac2e1&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /cashier/deposit/wallet-payment -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /cashier/deposit/wallet-payment -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/cashier/deposit/wallet-payment&#x27;),\n  headers: withSession(commonHeaders()),\n  body: {\n    amount: 1,\n    method: &#x27;pix&#x27;\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;d6d3bed3-0872-4a9b-8c50-271381b97c35&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /cashier/deposit/queue-notification -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /cashier/deposit/queue-notification -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/cashier/deposit/queue-notification&#x27;),\n  headers: withSession(commonHeaders()),\n  body: {\n    transactionId: &#x27;test-transaction&#x27;,\n    status: &#x27;ok&#x27;\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;8eaa4285-7c9d-4d9f-99e3-818a39ee6c69&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;DELETE /games/cache/all -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 DELETE /games/cache/all -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;DELETE&#x27;,\n  url: api(&#x27;/games/cache/all&#x27;),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;056701c0-fff6-4c9a-b67e-90e9601e4c46&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /games/get-free-games -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /games/get-free-games -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/games/get-free-games&#x27;),\n  headers: commonHeaders(),\n  qs: {\n    gameSymbol: &#x27;&#x27;,\n    language: &#x27;pt-BR&#x27;,\n    upcomingGames: false\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;c0d37aef-03e7-4650-8ba1-5e77b3c10573&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /games/get-group-games -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /games/get-group-games -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/games/get-group-games&#x27;),\n  headers: commonHeaders(),\n  qs: {\n    gameCodes: &#x27;1,2,3&#x27;,\n    language: &#x27;pt-BR&#x27;\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;0600e1dc-3d67-4bb6-bd59-3adca5a16309&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /games/get-list-group -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /games/get-list-group -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/games/get-list-group&#x27;),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;9c4d29df-4c1e-48d3-bba1-4afc2e3bbff0&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /cashier/deposit/wallet-withdrawal -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /cashier/deposit/wallet-withdrawal -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/cashier/deposit/wallet-withdrawal&#x27;),\n  headers: withSession(commonHeaders()),\n  body: {\n    amount: 1,\n    method: &#x27;pix&#x27;\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;c6fe815f-ce22-453b-b25a-1c4e6d0b44aa&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /games/get-real-games -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /games/get-real-games -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/games/get-real-games&#x27;),\n  headers: commonHeaders(),\n  qs: {\n    hasDemo: false,\n    language: &#x27;pt-BR&#x27;,\n    upcomingGames: false\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;e0b43891-283c-404c-8843-ea0fe477be9f&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /login -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /login -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/login&#x27;),\n  headers: commonHeaders(),\n  body: {\n    email: &#x27;<EMAIL>&#x27;,\n    password: &#x27;Password123&#x27;\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;cd0f38d9-49c6-4261-ba26-2ed251c4834b&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /login/logout -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /login/logout -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/login/logout&#x27;),\n  headers: withSession(commonHeaders()),\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;abd1f60b-0f4a-4735-b78e-708e1dfb2354&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /login/social -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /login/social -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/login/social&#x27;),\n  headers: commonHeaders(),\n  body: {\n    provider: &#x27;google&#x27;,\n    accessToken: &#x27;token&#x27;\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;f22ca1cc-0599-4c39-a869-dadb9e4d64c6&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /profile -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /profile -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/profile&#x27;),\n  headers: withSession(commonHeaders()),\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;4f158909-800f-4ab6-835c-098ad726aba6&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /profile/balance -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /profile/balance -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/profile/balance&#x27;),\n  headers: withSession(commonHeaders()),\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;daae1f58-2e5c-48ca-8861-e2573178a6b1&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /profile/kyc-url -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /profile/kyc-url -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/profile/kyc-url&#x27;),\n  headers: withSession(commonHeaders()),\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;8651883b-0e8d-4650-8c16-8c42ddd11c24&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /profile/last-played-games -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /profile/last-played-games -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/profile/last-played-games&#x27;),\n  headers: withSession(commonHeaders()),\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;b4939e38-ee66-463f-8d82-b921d9f1d33c&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /profile/send-auth-code -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /profile/send-auth-code -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/profile/send-auth-code&#x27;),\n  headers: withSession(commonHeaders()),\n  body: {\n    channel: &#x27;email&#x27;\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;a8bb084a-79c0-46ff-9a12-1c121eba85d6&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /profile/verify-auth-code -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /profile/verify-auth-code -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/profile/verify-auth-code&#x27;),\n  headers: withSession(commonHeaders()),\n  body: {\n    code: &#x27;123456&#x27;\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;5c9c86aa-78bb-4f9b-a3c8-f2295349c74b&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /promotions -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /promotions -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/promotions&#x27;),\n  headers: commonHeaders(),\n  qs: {\n    languageId: &#x27;pt&#x27;,\n    hiddenPromotion: false\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;6ce30d2a-cd6c-450e-9cbb-aba9384b9f62&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /promotions/{promotionId} -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /promotions/{promotionId} -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/promotions/1&#x27;),\n  headers: commonHeaders(),\n  qs: {\n    languageId: &#x27;pt&#x27;,\n    hiddenPromotion: false\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;47d126e5-8b55-4ac7-b456-0d3fba4d0d5a&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /utils/address -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /utils/address -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/utils/address&#x27;),\n  headers: _objectSpread(_objectSpread({}, commonHeaders()), {}, {\n    cep: &#x27;01001000&#x27;\n  }),\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;6cf0722b-c6fe-4270-8c56-c8b09fa49c5d&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /games/get-lobby-group-games -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /games/get-lobby-group-games -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/games/get-lobby-group-games&#x27;),\n  headers: commonHeaders(),\n  qs: {\n    language: &#x27;pt-BR&#x27;\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;033ae40c-cbc6-4fd9-a114-2cab343dd8f5&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /games/get-lobby-group-games-paginated -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /games/get-lobby-group-games-paginated -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/games/get-lobby-group-games-paginated&#x27;),\n  headers: commonHeaders(),\n  qs: {\n    language: &#x27;pt-BR&#x27;,\n    page: 1,\n    pageSize: 20\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;73167b46-ec3f-4a25-a630-aef2f4169eb2&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /games/search-games -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /games/search-games -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/games/search-games&#x27;),\n  headers: commonHeaders(),\n  qs: {\n    language: &#x27;pt-BR&#x27;,\n    search: &#x27;game&#x27;\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;ccbbfc3a-988d-4992-af1f-d58af4bd7489&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /home -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /home -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/home&#x27;),\n  headers: commonHeaders(),\n  qs: {\n    platform: &#x27;web&#x27;\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;6f67350b-b66d-4314-bf9b-6d27e6e804bb&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/home/<USER>/management/bonusLed -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /management/bonusLed -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/management/bonusLed&#x27;),\n  headers: commonHeaders(),\n  body: {},\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;819bb10d-6d53-466d-82ef-0264eee40c0a&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /management/matches -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /management/matches -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/management/matches&#x27;),\n  headers: commonHeaders(),\n  body: {},\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;c05844d8-a947-4c89-b0d2-b9a0fc90272c&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /management/sportsBetting -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /management/sportsBetting -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/management/sportsBetting&#x27;),\n  headers: commonHeaders(),\n  body: {},\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;4e83fee1-f0ec-4baa-ba78-68715627e36f&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /management/stories -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /management/stories -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/management/stories&#x27;),\n  headers: commonHeaders(),\n  body: {},\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;01335e23-9c1a-4dbf-abc8-c05b546782fe&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /management/top-scorers -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /management/top-scorers -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/management/top-scorers&#x27;),\n  headers: commonHeaders(),\n  body: {},\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;b3db3ddc-6943-4cb6-8403-c895ab69c36f&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /profile/kyc-details -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /profile/kyc-details -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/profile/kyc-details&#x27;),\n  headers: withSession(commonHeaders()),\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;9163990d-a4e2-4d99-9417-0c764ccce9f5&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /profile/third-party-kyc -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /profile/third-party-kyc -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/profile/third-party-kyc&#x27;),\n  headers: withSession(commonHeaders()),\n  body: {},\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;5141e0d0-385f-4964-b330-44a038d17275&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /profile/update-profile -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /profile/update-profile -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/profile/update-profile&#x27;),\n  headers: withSession(commonHeaders()),\n  body: {},\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;eb49fbf4-ff51-48b6-b4fb-1a46ba98c4e2&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /reviews -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /reviews -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/reviews&#x27;),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;ec85aa91-**************-af80a9ba06f0&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /reviews/export -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /reviews/export -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/reviews/export&#x27;),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;37fa8c34-e0c8-438e-a778-03ea40af54a2&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /stories -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /stories -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/stories&#x27;),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;bf611fd6-4626-47a3-94bc-885c5cf486e4&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /utils -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /utils -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/utils&#x27;),\n  headers: commonHeaders(),\n  body: {},\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;d029b9af-f1a6-448e-b893-bb21a1cd6985&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /utils/active-bonus -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /utils/active-bonus -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/utils/active-bonus&#x27;),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;edf117e3-1bab-4b18-981f-934e9e27dcf0&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /utils/bonus -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /utils/bonus -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/utils/bonus&#x27;),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;cb9e47a1-1143-4197-a1ec-ade9cfeb4c78&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /utils/claim-bonus -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /utils/claim-bonus -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/utils/claim-bonus&#x27;),\n  headers: commonHeaders(),\n  qs: {\n    accept: true\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;cd1a2dab-5581-4343-a4f2-913309589d8b&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /utils/drop-bonus -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /utils/drop-bonus -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/utils/drop-bonus&#x27;),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;9c9af987-b1ed-434b-8978-48cc8d4a499a&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /utils/phoneavailability -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /utils/phoneavailability -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/utils/phoneavailability&#x27;),\n  headers: commonHeaders(),\n  body: {\n    phone: &#x27;+5500000000&#x27;\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;01a6c04c-61a6-4fdc-916e-ad2f949d715f&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /utils/referral-eligible -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /utils/referral-eligible -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/utils/referral-eligible&#x27;),\n  headers: withSession(commonHeaders()),\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;8681046a-e7f6-4d0a-8a05-1b567262d6e4&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /utils/social -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /utils/social -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/utils/social&#x27;),\n  headers: commonHeaders(),\n  body: {},\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;f85ea03c-fe9a-473b-9a85-1255104c983a&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;GET /utils/user-acknowledgement -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 GET /utils/user-acknowledgement -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;GET&#x27;,\n  url: api(&#x27;/utils/user-acknowledgement&#x27;),\n  headers: withSession(commonHeaders()),\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;9b37695b-63b3-44a2-9df3-89b04e67bfe6&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /utils/user-policy-details -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /utils/user-policy-details -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/utils/user-policy-details&#x27;),\n  headers: withSession(commonHeaders()),\n  body: {},\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;e142d55d-976a-4995-bfe3-ceea55c49ca2&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /utils/validatecpf -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /utils/validatecpf -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/utils/validatecpf&#x27;),\n  headers: commonHeaders(),\n  body: {\n    cpf: &#x27;00000000000&#x27;\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;5ec04db5-5780-4769-8be6-63e587abbddf&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /utils/validateemail -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /utils/validateemail -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/utils/validateemail&#x27;),\n  headers: commonHeaders(),\n  body: {\n    email: &#x27;<EMAIL>&#x27;\n  },\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;99315f8f-e4b6-4a78-abbe-5b9de28f9bd6&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /cashier/deposit/update-bank-account -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /cashier/deposit/update-bank-account -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/cashier/deposit/update-bank-account&#x27;),\n  headers: withSession(commonHeaders()),\n  body: {},\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;d1d970fb-5cd2-49fb-a2d7-84c2c7b7b38c&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;POST /login/social/integration -&gt; 417&quot;,&quot;fullTitle&quot;:&quot;Platform API - smoke 417 POST /login/social/integration -&gt; 417&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: api(&#x27;/login/social/integration&#x27;),\n  headers: commonHeaders(),\n  body: {},\n  failOnStatusCode: false\n}).then(res =&gt; {\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;0e7263bd-7b40-4dab-ad83-1115770e3963&quot;,&quot;parentUUID&quot;:&quot;11e23cd9-716c-4b92-8f86-9724090e5c29&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[&quot;a7d762cd-a13a-4913-8d76-aed399385178&quot;,&quot;9d97d1cf-b2bf-46f6-aba3-ff5246be3d7a&quot;,&quot;2317aefa-a985-4dfd-97d3-6dd7a4ee0d90&quot;,&quot;2fe5048e-0639-462f-b31d-6466d6cac2e1&quot;,&quot;d6d3bed3-0872-4a9b-8c50-271381b97c35&quot;,&quot;8eaa4285-7c9d-4d9f-99e3-818a39ee6c69&quot;,&quot;056701c0-fff6-4c9a-b67e-90e9601e4c46&quot;,&quot;c0d37aef-03e7-4650-8ba1-5e77b3c10573&quot;,&quot;0600e1dc-3d67-4bb6-bd59-3adca5a16309&quot;,&quot;9c4d29df-4c1e-48d3-bba1-4afc2e3bbff0&quot;,&quot;c6fe815f-ce22-453b-b25a-1c4e6d0b44aa&quot;,&quot;e0b43891-283c-404c-8843-ea0fe477be9f&quot;,&quot;cd0f38d9-49c6-4261-ba26-2ed251c4834b&quot;,&quot;abd1f60b-0f4a-4735-b78e-708e1dfb2354&quot;,&quot;f22ca1cc-0599-4c39-a869-dadb9e4d64c6&quot;,&quot;4f158909-800f-4ab6-835c-098ad726aba6&quot;,&quot;daae1f58-2e5c-48ca-8861-e2573178a6b1&quot;,&quot;8651883b-0e8d-4650-8c16-8c42ddd11c24&quot;,&quot;b4939e38-ee66-463f-8d82-b921d9f1d33c&quot;,&quot;a8bb084a-79c0-46ff-9a12-1c121eba85d6&quot;,&quot;5c9c86aa-78bb-4f9b-a3c8-f2295349c74b&quot;,&quot;6ce30d2a-cd6c-450e-9cbb-aba9384b9f62&quot;,&quot;47d126e5-8b55-4ac7-b456-0d3fba4d0d5a&quot;,&quot;6cf0722b-c6fe-4270-8c56-c8b09fa49c5d&quot;,&quot;033ae40c-cbc6-4fd9-a114-2cab343dd8f5&quot;,&quot;73167b46-ec3f-4a25-a630-aef2f4169eb2&quot;,&quot;ccbbfc3a-988d-4992-af1f-d58af4bd7489&quot;,&quot;6f67350b-b66d-4314-bf9b-6d27e6e804bb&quot;,&quot;993e4c57-f009-43f4-933b-54f2223ecb93&quot;,&quot;44d8651e-e6eb-4972-b5cd-b8388661b1f0&quot;,&quot;057b8b27-a378-4d46-9853-6d6b38580149&quot;,&quot;1732b45f-3f62-49d0-8192-ccfb2144d68e&quot;,&quot;025e0763-09ff-4ea0-a7d4-660c0bc606ea&quot;,&quot;e9551841-94e5-4d77-9cb4-5ee228389e5a&quot;,&quot;10d40958-45ea-473b-bd03-a560855a47da&quot;,&quot;42fc72e1-c2cd-4e5f-8aa9-05b7851e925a&quot;,&quot;166b8965-4d17-457b-a20d-fd2abdbb1675&quot;,&quot;819bb10d-6d53-466d-82ef-0264eee40c0a&quot;,&quot;c05844d8-a947-4c89-b0d2-b9a0fc90272c&quot;,&quot;4e83fee1-f0ec-4baa-ba78-68715627e36f&quot;,&quot;01335e23-9c1a-4dbf-abc8-c05b546782fe&quot;,&quot;b3db3ddc-6943-4cb6-8403-c895ab69c36f&quot;,&quot;9163990d-a4e2-4d99-9417-0c764ccce9f5&quot;,&quot;5141e0d0-385f-4964-b330-44a038d17275&quot;,&quot;eb49fbf4-ff51-48b6-b4fb-1a46ba98c4e2&quot;,&quot;ec85aa91-**************-af80a9ba06f0&quot;,&quot;37fa8c34-e0c8-438e-a778-03ea40af54a2&quot;,&quot;bf611fd6-4626-47a3-94bc-885c5cf486e4&quot;,&quot;d029b9af-f1a6-448e-b893-bb21a1cd6985&quot;,&quot;edf117e3-1bab-4b18-981f-934e9e27dcf0&quot;,&quot;cb9e47a1-1143-4197-a1ec-ade9cfeb4c78&quot;,&quot;cd1a2dab-5581-4343-a4f2-913309589d8b&quot;,&quot;9c9af987-b1ed-434b-8978-48cc8d4a499a&quot;,&quot;01a6c04c-61a6-4fdc-916e-ad2f949d715f&quot;,&quot;8681046a-e7f6-4d0a-8a05-1b567262d6e4&quot;,&quot;f85ea03c-fe9a-473b-9a85-1255104c983a&quot;,&quot;9b37695b-63b3-44a2-9df3-89b04e67bfe6&quot;,&quot;e142d55d-976a-4995-bfe3-ceea55c49ca2&quot;,&quot;5ec04db5-5780-4769-8be6-63e587abbddf&quot;,&quot;99315f8f-e4b6-4a78-abbe-5b9de28f9bd6&quot;,&quot;d1d970fb-5cd2-49fb-a2d7-84c2c7b7b38c&quot;,&quot;0e7263bd-7b40-4dab-ad83-1115770e3963&quot;],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000}],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:true,&quot;rootEmpty&quot;:true,&quot;_timeout&quot;:2000},{&quot;uuid&quot;:&quot;41202a94-2153-4023-a442-48488278ce95&quot;,&quot;title&quot;:&quot;&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\Bonus.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\Bonus.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[],&quot;suites&quot;:[{&quot;uuid&quot;:&quot;fcd74623-3d68-44c5-9c28-c6ae8e63c172&quot;,&quot;title&quot;:&quot;Cadastro&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\Bonus.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\Bonus.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Deve validar os elementos da tela de saque&quot;,&quot;fullTitle&quot;:&quot;Cadastro Deve validar os elementos da tela de saque&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.get(&#x27;.nebulosa-header__desktopButtonWrapper &gt; .nebulosa-avatar__avatarContainer &gt; .nebulosa-avatar__root &gt; .nebulosa-avatar__fallback&#x27;).should(&#x27;be.visible&#x27;).click();&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;6836978e-991d-46b6-94a2-d0a3e2b3a4fc&quot;,&quot;parentUUID&quot;:&quot;fcd74623-3d68-44c5-9c28-c6ae8e63c172&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[&quot;6836978e-991d-46b6-94a2-d0a3e2b3a4fc&quot;],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000}],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:true,&quot;rootEmpty&quot;:true,&quot;_timeout&quot;:2000},{&quot;uuid&quot;:&quot;b88ea454-36e8-4471-b0ca-7c1a964a25b8&quot;,&quot;title&quot;:&quot;&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\Cadastro.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\Cadastro.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[],&quot;suites&quot;:[{&quot;uuid&quot;:&quot;4301bff2-57bf-4c79-b5ad-cbac5e7fdc47&quot;,&quot;title&quot;:&quot;Cadastro&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\Cadastro.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\Cadastro.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Deve validar elementos no cadastro via botão do header&quot;,&quot;fullTitle&quot;:&quot;Cadastro Deve validar elementos no cadastro via botão do header&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:7363,&quot;state&quot;:&quot;failed&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:true,&quot;pending&quot;:false,&quot;context&quot;:null,&quot;code&quot;:&quot;(0, _blockTrackers.blockTrackers)();\ncy.visit(url);\ncy.log(&#x27;Acessando cadastro via botão do header&#x27;);\ncy.contains(&#x27;button&#x27;, &#x27;Cadastrar&#x27;);\ncy.get(_locators.default.CADASTRO.BOTAO_CADASTRAR_HEADER).click();\ncy.validateRegistrationPageElements();&quot;,&quot;err&quot;:{&quot;message&quot;:&quot;AssertionError: Timed out retrying after 4000ms: Expected to find content: &#x27;Cadastrar&#x27; within the selector: &#x27;button&#x27; but never did.&quot;,&quot;estack&quot;:&quot;AssertionError: Timed out retrying after 4000ms: Expected to find content: &#x27;Cadastrar&#x27; within the selector: &#x27;button&#x27; but never did.\n    at Context.eval (webpack://EstrelaBet/./cypress/e2e/FrontEnd/Cadastro.js:18:11)&quot;,&quot;diff&quot;:null},&quot;uuid&quot;:&quot;3a2a7115-fa0b-4bdf-844e-a42e1d46a0cf&quot;,&quot;parentUUID&quot;:&quot;4301bff2-57bf-4c79-b5ad-cbac5e7fdc47&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[],&quot;failures&quot;:[&quot;3a2a7115-fa0b-4bdf-844e-a42e1d46a0cf&quot;],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:7363,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000}],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:true,&quot;rootEmpty&quot;:true,&quot;_timeout&quot;:2000},{&quot;uuid&quot;:&quot;0397bf6c-f68c-458a-b2e0-c435a18b06cb&quot;,&quot;title&quot;:&quot;&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\Cassino.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\Cassino.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[],&quot;suites&quot;:[{&quot;uuid&quot;:&quot;2246568d-0bf5-4459-92ae-c2e053fa9b06&quot;,&quot;title&quot;:&quot;Deve abrir a página de cassino&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\Cassino.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\Cassino.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Acessa pagina de Cassino e valida elementos&quot;,&quot;fullTitle&quot;:&quot;Deve abrir a página de cassino Acessa pagina de Cassino e valida elementos&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:13329,&quot;state&quot;:&quot;failed&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:true,&quot;pending&quot;:false,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.contains(&#x27;a[href=\&quot;/cassino-online\&quot;]&#x27;, &#x27;Cassino&#x27;);\ncy.contains(&#x27;a[href=\&quot;/cassino-online\&quot;]&#x27;, &#x27;Cassino&#x27;).click();\n// Valida o botão de busca (ícone de lupa)\ncy.get(&#x27;a[href=\&quot;/jogos/busca\&quot;]&#x27;).should(&#x27;be.visible&#x27;);\n// Lista de labels esperadas\nconst botoes = [&#x27;Inicial&#x27;, &#x27;Torneios&#x27;, &#x27;Slots&#x27;, &#x27;Crash Games&#x27;, &#x27;Comprar Bônus&#x27;, &#x27;Novos Jogos&#x27;,\n//&#x27;Jackpots&#x27;,\n&#x27;Mais&#x27;];\n// Valida cada botão\nbotoes.forEach(label =&gt; {\n  cy.contains(&#x27;.nebulosa-button__buttonLabel&#x27;, label).should(&#x27;be.visible&#x27;);\n});\ncy.clickByContains(&#x27;.nebulosa-button__buttonLabel&#x27;, &#x27;Mais&#x27;);\n// Lista de labels esperadas &gt; Tem que pensar em uma logica trazendo o fluxo do back na BFF\nconst categorias = [&#x27;Populares&#x27;, &#x27;Jogos de Fortuna&#x27;, &#x27;Games Global&#x27;, &#x27;Estrela Indica&#x27;, &#x27;Novos Jogos&#x27;, &#x27;Pragmatic Play&#x27;, &#x27;Missão Lucky Rush&#x27;, &#x27;Crash Games&#x27;,\n//&#x27;Plinko&#x27;, removido 26/08 \n&#x27;Comprar Bônus&#x27;, &#x27;Slots&#x27;, &#x27;Jogos Ao Vivo&#x27;,\n//&#x27;Jackpots&#x27;,\n&#x27;Em breve&#x27;, &#x27;Provedores&#x27;];\n// Valida cada label visível\ncategorias.forEach(label =&gt; {\n  cy.contains(&#x27;li&#x27;, label).should(&#x27;be.visible&#x27;);\n});\n// Fecha o modal (botão X no header)\ncy.get(&#x27;.nebulosa-modal__HeaderRight .nebulosa-modal__IconAction&#x27;).click();\n// Valida que o modal fechou\ncy.get(&#x27;[role=\&quot;dialog\&quot;]&#x27;).should(&#x27;not.exist&#x27;);\ncy.log(&#x27;Validar montagem das sessões do cassino&#x27;);\ncy.isVisible(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Populares&#x27;);\ncy.isVisible(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Jogos de Fortuna&#x27;);\ncy.isVisible(&#x27;header&#x27;, &#x27;Top 10 - Cassino&#x27;);\ncy.isVisible(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Games Global&#x27;);\ncy.isVisible(&#x27;header&#x27;, &#x27;Mais premiados&#x27;);\ncy.isVisible(&#x27;header&#x27;, &#x27;Menos premiados&#x27;);\ncy.isVisible(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Novos Jogos&#x27;);\ncy.isVisible(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Pragmatic Play&#x27;);\ncy.isVisible(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Missão Lucky Rush&#x27;);\ncy.isVisible(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Torneio Favoritos da RubyPlay&#x27;);\ncy.isVisible(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Crash Games&#x27;);\ncy.isVisible(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Amusnet&#x27;);\ncy.isVisible(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;100% Brasil&#x27;);\ncy.isVisible(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Comprar Bônus&#x27;);\ncy.isVisible(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Slots&#x27;);\ncy.isVisible(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Jogos Ao Vivo&#x27;);\ncy.isVisible(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Em breve&#x27;);\ncy.isVisible(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Provedores de jogos&#x27;);&quot;,&quot;err&quot;:{&quot;message&quot;:&quot;AssertionError: Timed out retrying after 4000ms: Expected to find content: &#x27;Aceitar todos os cookies&#x27; within the element: &lt;button.nebulosa-button__root.nebulosa-button__root--size_Large.nebulosa-button__root--variant_primary.nebulosa-button__root--loading_true.nebulosa-button__root--disabled_true&gt; but never did.\n\nThis error occurred while creating the session. Because the session setup failed, we failed the test.\n\nBecause this error occurred during a `before each` hook we are skipping the remaining tests in the current suite: `Deve abrir a página de cassino`&quot;,&quot;estack&quot;:&quot;AssertionError: Timed out retrying after 4000ms: Expected to find content: &#x27;Aceitar todos os cookies&#x27; within the element: &lt;button.nebulosa-button__root.nebulosa-button__root--size_Large.nebulosa-button__root--variant_primary.nebulosa-button__root--loading_true.nebulosa-button__root--disabled_true&gt; but never did.\n\nThis error occurred while creating the session. Because the session setup failed, we failed the test.\n\nBecause this error occurred during a `before each` hook we are skipping the remaining tests in the current suite: `Deve abrir a página de cassino`\n    at Object.eval [as setup] (webpack://EstrelaBet/./cypress/support/commands.js:201:17)\n    at Context.&lt;anonymous&gt; (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:136486:38)\n    at getRet (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:121549:20)\n    at tryCatcher (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:1777:23)\n    at Promise.attempt.Promise.try (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:4285:29)\n    at Context.thenFn (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:121560:66)\n    at Context.then (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:121811:21)\n    at wrapped (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:141517:19)&quot;,&quot;diff&quot;:null},&quot;uuid&quot;:&quot;33d377f7-b4ff-4413-b87c-6980ce223d4f&quot;,&quot;parentUUID&quot;:&quot;2246568d-0bf5-4459-92ae-c2e053fa9b06&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Acessa pagina de Cassino e acessar um jogo&quot;,&quot;fullTitle&quot;:&quot;Deve abrir a página de cassino Acessa pagina de Cassino e acessar um jogo&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;skipped&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.contains(&#x27;a&#x27;, &#x27;Cassino&#x27;);\ncy.contains(&#x27;a&#x27;, &#x27;Cassino&#x27;).click();\n// Acessar jogos\ncy.get(&#x27;img[alt=\&quot;Aviator-game-img\&quot;]&#x27;).click();\n//Caso o usuario nao tenha verificado a identidade, clica no botão \&quot;Não quero verificar agora\&quot;\ncy.get(&#x27;body&#x27;).then($body =&gt; {\n  // Verifica se o botão existe\n  if ($body.find(&#x27;button:contains(\&quot;Não quero verificar agora\&quot;)&#x27;).length) {\n    cy.contains(&#x27;button&#x27;, &#x27;Não quero verificar agora&#x27;).click();\n  } else {\n    cy.log(&#x27;Modal não apareceu, continuando o teste...&#x27;);\n  }\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;a2d99d14-8dd9-41d9-b804-161722cf4741&quot;,&quot;parentUUID&quot;:&quot;2246568d-0bf5-4459-92ae-c2e053fa9b06&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:true}],&quot;suites&quot;:[],&quot;passes&quot;:[],&quot;failures&quot;:[&quot;33d377f7-b4ff-4413-b87c-6980ce223d4f&quot;],&quot;pending&quot;:[],&quot;skipped&quot;:[&quot;a2d99d14-8dd9-41d9-b804-161722cf4741&quot;],&quot;duration&quot;:13329,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000}],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:true,&quot;rootEmpty&quot;:true,&quot;_timeout&quot;:2000},{&quot;uuid&quot;:&quot;66acddb5-72e8-4f5a-a799-781095e369f5&quot;,&quot;title&quot;:&quot;&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\Deposito.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\Deposito.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[],&quot;suites&quot;:[{&quot;uuid&quot;:&quot;48ac7edf-aee2-4ad3-83fb-89d356380c4b&quot;,&quot;title&quot;:&quot;Deve validar a geração de pix&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\Deposito.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\Deposito.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Gerar pix com sucesso&quot;,&quot;fullTitle&quot;:&quot;Deve validar a geração de pix Gerar pix com sucesso&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:30089,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;slow&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.get(&#x27;.nebulosa-header__buttomAndBadgeWrapper&#x27;).contains(&#x27;button&#x27;, &#x27;Depositar&#x27;);\ncy.clickByContains(&#x27;.nebulosa-header__buttomAndBadgeWrapper&#x27;, &#x27;Depositar&#x27;);\n//Validando modal de valores\n//Título\ncy.contains(&#x27;h1&#x27;, &#x27;Depósito rápido&#x27;).should(&#x27;be.visible&#x27;);\n//Título campo e valor pré selecionado\ncy.validateText(&#x27;.nebulosa-input__Root &gt; label&#x27;, &#x27;Valor do depósito&#x27;);\ncy.get(&#x27;.nebulosa-input__Input &gt; input:eq(1)&#x27;).should(&#x27;have.value&#x27;, &#x27;R$ 50,00&#x27;);\n//Validando opções disponíveis para depósito\ncy.validateText(&#x27;.d_flex &gt; div &gt; h3&#x27;, &#x27;Valor mínimo R$ 1,00 e valor máximo R$ 45.000,00&#x27;);\ncy.validateText(&#x27;[type=\&quot;button\&quot;] &gt; span:eq(0)&#x27;, &#x27;R$ 10&#x27;);\ncy.validateText(&#x27;[type=\&quot;button\&quot;] &gt; span:eq(1)&#x27;, &#x27;R$ 50&#x27;);\ncy.validateText(&#x27;[type=\&quot;button\&quot;] &gt; span:eq(2)&#x27;, &#x27;R$ 100&#x27;);\ncy.validateText(&#x27;[type=\&quot;button\&quot;] &gt; span:eq(3)&#x27;, &#x27;R$ 250&#x27;);\ncy.validateText(&#x27;[type=\&quot;button\&quot;] &gt; span:eq(4)&#x27;, &#x27;R$ 500&#x27;);\ncy.validateText(&#x27;[type=\&quot;button\&quot;] &gt; span:eq(5)&#x27;, &#x27;R$ 1.000&#x27;);\n//validando botão e mensagem de maior de 18\ncy.validateText(&#x27;[type=\&quot;submit\&quot;]&#x27;, &#x27;Depositar&#x27;);\n//cy.validateText(&#x27;form &gt; div &gt; .d_flex &gt; .ai_center &gt; p&#x27;, &#x27;Depósitos proibidos para menores de 18 anos.&#x27;)\n//Botão x\ncy.isVisible(&#x27;[data-testid=\&quot;closeDepositModalButton\&quot;]&#x27;);\n//Banner principal\ncy.isVisible(&#x27;img[src*=\&quot;deposit-banner.png\&quot;]&#x27;);\n//clicando para avançar\ncy.clickMouse(&#x27;[type=\&quot;submit\&quot;]&#x27;, &#x27;Depositar&#x27;);\ncy.wait(&#x27;@gerandoQrCode&#x27;, {\n  timeout: 15000\n}).its(&#x27;response.statusCode&#x27;).should(&#x27;eq&#x27;, 200);\n//Label depósito/pix\ncy.clickByContains(&#x27;h1&#x27;, &#x27;Código Pix disponível&#x27;);\ncy.clickByContains(&#x27;p&#x27;, &#x27;Copie o código e utilize o PIX Copia e Cola no aplicativo do seu banco.&#x27;);\ncy.isVisible(&#x27;button&#x27;, &#x27;Saiba como fazer o pagamento com Pix&#x27;);\ncy.clickByContains(&#x27;button&#x27;, &#x27;Saiba como fazer o pagamento com Pix&#x27;);\n//Listagem com a informaços do Saiba como fazer o pagamento com Pix\ncy.get(&#x27;ul &gt; ul&#x27;).eq(0).within(() =&gt; {\n  cy.contains(&#x27;p&#x27;, &#x27;1. Acesse o aplicativo do seu banco e escolha a opção Pix Copia e Cola” e cole o código&#x27;).should(&#x27;be.visible&#x27;);\n});\ncy.get(&#x27;ul &gt; ul&#x27;).eq(1).within(() =&gt; {\n  cy.contains(&#x27;p&#x27;, &#x27;2. Confirme as informações e finalize o pagamento&#x27;).should(&#x27;be.visible&#x27;);\n});\ncy.get(&#x27;ul &gt; ul&#x27;).eq(2).within(() =&gt; {\n  cy.contains(&#x27;p&#x27;, &#x27;3. Seu pagamento será aprovado em alguns segundos!&#x27;).should(&#x27;be.visible&#x27;);\n});\ncy.isVisible(&#x27;p&#x27;, &#x27;Pagamentos feitos por outro CPF ou de contas empresariais serão automaticamente rejeitados&#x27;);\ncy.isVisible(&#x27;button&#x27;, &#x27;Exibir QR Code&#x27;);\ncy.clickByContains(&#x27;button&#x27;, &#x27;Exibir QR Code&#x27;);\ncy.get(&#x27;img&#x27;).should(&#x27;exist&#x27;);\n// Captura o valor inicial do contador\ncy.get(&#x27;span.fv-num_tabular-nums&#x27;).invoke(&#x27;text&#x27;).then(initialValue =&gt; {\n  // Espera 1 segundo (ou o tempo que o contador deve mudar)\n  cy.wait(1000);\n  // Captura novamente e compara\n  cy.get(&#x27;span.fv-num_tabular-nums&#x27;).invoke(&#x27;text&#x27;).should(newValue =&gt; {\n    expect(newValue).to.not.eq(initialValue);\n  });\n});\ncy.validateText(&#x27;.gap_md &gt; .mb_xxxs &gt; .fs_xxs&#x27;, &#x27;Valor do depósito:&#x27;);\ncy.wait(2000);\ncy.contains(&#x27;.mb_xxxs &gt; .fs_xs&#x27;, &#x27;R$ 50,00&#x27;).should(&#x27;be.visible&#x27;);\n//botao Copiar código Pix\ncy.contains(&#x27;button&#x27;, &#x27;Copiar código Pix&#x27;).should(&#x27;have.attr&#x27;, &#x27;lefticon&#x27;, &#x27;brand-pix&#x27;);\ncy.stubClipboard();\ncy.clickMouse(&#x27;[lefticon=\&quot;brand-pix\&quot;] &gt; span&#x27;);\n// validar que tentou copiar:\ncy.get(&#x27;@clipboardWrite&#x27;).should(&#x27;have.been.called&#x27;);\n//Validar mensagem de sucesso\ncy.get(&#x27;div.nebulosa-toast__descriptionText&#x27;).should(&#x27;be.visible&#x27;).and(&#x27;contain.text&#x27;, &#x27;Código PIX copiado!&#x27;);&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;ccad1c4e-095d-4dc8-9f19-8e5b7291e5b5&quot;,&quot;parentUUID&quot;:&quot;48ac7edf-aee2-4ad3-83fb-89d356380c4b&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[&quot;ccad1c4e-095d-4dc8-9f19-8e5b7291e5b5&quot;],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:30089,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000}],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:true,&quot;rootEmpty&quot;:true,&quot;_timeout&quot;:2000},{&quot;uuid&quot;:&quot;0c1d8dbd-ed5c-4160-b42a-750e55f54643&quot;,&quot;title&quot;:&quot;&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\Esportes.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\Esportes.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[],&quot;suites&quot;:[{&quot;uuid&quot;:&quot;06e6a7b4-ab7f-4ddf-990f-f1b2fcbeca54&quot;,&quot;title&quot;:&quot;Deve validar opções da conta&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\Esportes.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\Esportes.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Validando componentes e textos da pagina no Sports Book Sidebar Esquerda&quot;,&quot;fullTitle&quot;:&quot;Deve validar opções da conta Validando componentes e textos da pagina no Sports Book Sidebar Esquerda&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;// Acessa a página de esportes\ncy.contains(&#x27;label &gt; [href=\&quot;/aposta-esportiva\&quot;]&#x27;, &#x27;Esportes&#x27;).click();\n// Aguarda a página base e o iframe ficarem prontos\ncy.waitLoading();\ncy.waitForSportsIframeReady(&#x27;iframe.aut-iframe&#x27;, &#x27;input[placeholder=\&quot;Introduzir nome do time ou do campeonato\&quot;]&#x27;);\n// Depois de pronto, valide o campo específico\ncy.switchToIframe(&#x27;iframe.aut-iframe&#x27;).find(&#x27;input[placeholder=\&quot;Introduzir nome do time ou do campeonato\&quot;]&#x27;, {\n  timeout: 30000\n}).should(&#x27;be.visible&#x27;).and(&#x27;have.attr&#x27;, &#x27;placeholder&#x27;, &#x27;Introduzir nome do time ou do campeonato&#x27;);&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;fe2eafee-caf1-4a2e-b01e-a9b1ab89c22a&quot;,&quot;parentUUID&quot;:&quot;06e6a7b4-ab7f-4ddf-990f-f1b2fcbeca54&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[&quot;fe2eafee-caf1-4a2e-b01e-a9b1ab89c22a&quot;],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000}],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:true,&quot;rootEmpty&quot;:true,&quot;_timeout&quot;:2000},{&quot;uuid&quot;:&quot;3b0719d7-24b3-483a-b8a9-d873d45586ec&quot;,&quot;title&quot;:&quot;&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\Footer.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\Footer.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[],&quot;suites&quot;:[{&quot;uuid&quot;:&quot;92225da3-829e-443e-a9f3-cb5aaaeadeb4&quot;,&quot;title&quot;:&quot;Deve abrir a página de cassino&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\Footer.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\Footer.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Valida elementos do footer&quot;,&quot;fullTitle&quot;:&quot;Deve abrir a página de cassino Valida elementos do footer&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:6851,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;medium&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;context&quot;:null,&quot;code&quot;:&quot;(0, _blockTrackers.blockTrackers)();\ncy.visit(url);\ncy.get(&#x27;img[alt=\&quot;Estrelabet\&quot;]&#x27;).should(&#x27;be.visible&#x27;);\ncy.get(&#x27;a[href*=\&quot;facebook.com\&quot;] img[alt=\&quot;facebook\&quot;]&#x27;).should(&#x27;be.visible&#x27;);\ncy.get(&#x27;a[href*=\&quot;twitter.com\&quot;] img[alt=\&quot;twitter\&quot;]&#x27;).should(&#x27;be.visible&#x27;);\ncy.get(&#x27;a[href*=\&quot;youtube.com\&quot;] img[alt=\&quot;youtube\&quot;]&#x27;).should(&#x27;be.visible&#x27;);\ncy.get(&#x27;a[href*=\&quot;instagram.com\&quot;] img[alt=\&quot;instagram\&quot;]&#x27;).should(&#x27;be.visible&#x27;);\ncy.get(&#x27;a[href*=\&quot;t.me\&quot;] img[alt=\&quot;telegram\&quot;]&#x27;).should(&#x27;be.visible&#x27;);\ncy.get(&#x27;a[href*=\&quot;tiktok.com\&quot;] img[alt=\&quot;tiktok\&quot;]&#x27;).should(&#x27;be.visible&#x27;);\ncy.get(&#x27;a[href*=\&quot;blog.estrelabet.com\&quot;] img[alt=\&quot;blog\&quot;]&#x27;).should(&#x27;be.visible&#x27;);\ncy.contains(&#x27;span&#x27;, &#x27;Baixe o Aplicativo EstrelaBet&#x27;).should(&#x27;be.visible&#x27;);\ncy.get(&#x27;img[alt=\&quot;Disponível no Google Play\&quot;]&#x27;).should(&#x27;be.visible&#x27;);\ncy.get(&#x27;a[href*=\&quot;play.google.com\&quot;]&#x27;).should(&#x27;have.attr&#x27;, &#x27;href&#x27;).and(&#x27;include&#x27;, &#x27;play.google.com&#x27;);\n// \&quot;Aposte\&quot;\ncy.contains(\&quot;span\&quot;, \&quot;Aposte\&quot;).should(\&quot;be.visible\&quot;);\n// 19/08/2025 mudaram o locator\n//cy.validateText(&#x27;.StaticFooter_footer-links-section__0Fi_h&#x27;, &#x27;Aposte&#x27;)\ncy.validateText(&#x27;[href=\&quot;https://www.estrelabet.bet.br/aposta-esportiva\&quot;]&#x27;, &#x27;Apostas esportivas&#x27;);\ncy.validateText(&#x27;[href=\&quot;https://www.estrelabet.bet.br/gameplay/fortune-tiger\&quot;]&#x27;, &#x27;Fortune Tiger&#x27;);\ncy.validateText(&#x27;[href=\&quot;https://www.estrelabet.bet.br/gameplay/fortune-rabbit\&quot;]&#x27;, &#x27;Fortune Rabbit&#x27;);\ncy.validateText(&#x27;[href=\&quot;https://play.google.com/store/apps/details?id=co.br.bet.estrelabet.app&amp;pli=1\&quot;]&#x27;, &#x27;App de Apostas&#x27;);\n// \&quot;Links úteis\&quot;\n//cy.validateText(&#x27;.StaticFooter_footer-links-header__U2WL_&#x27;, &#x27;Links úteis&#x27;)\ncy.contains(\&quot;span\&quot;, \&quot;Links úteis\&quot;).should(\&quot;be.visible\&quot;);\ncy.validateText(&#x27;[href=\&quot;https://comunidade.estrelabet.com/\&quot;]&#x27;, &#x27;Comunidade&#x27;);\ncy.validateText(&#x27;[href=\&quot;https://www.estrelabet.bet.br/pb/offers\&quot;]&#x27;, &#x27;Ofertas&#x27;);\ncy.validateText(&#x27;[href=\&quot;https://www.estrelabet.bet.br/page/responsible-gaming\&quot;] &gt; span:eq(0)&#x27;, &#x27;Jogo responsável&#x27;);\ncy.validateText(&#x27;[href=\&quot;https://blog.estrelabet.com/\&quot;]  &gt; span:eq(0)&#x27;, &#x27;Blog&#x27;);\n// \&quot;Regras\&quot;\ncy.contains(\&quot;span\&quot;, \&quot;Regras\&quot;).should(\&quot;be.visible\&quot;);\n//cy.validateText(&#x27;.StaticFooter_footer-links-header__U2WL_&#x27;, &#x27;Regras&#x27;)\ncy.validateText(&#x27;[href=\&quot;https://www.estrelabet.bet.br/pb/politica/termos-e-condicoes\&quot;]&#x27;, &#x27;Termos e Condições Gerais&#x27;); //[href=\&quot;https://www.estrelabet.bet.br/politica/termos-e-condicoes\ncy.validateText(&#x27;[href=\&quot;https://www.estrelabet.bet.br/page/responsible-gaming\&quot;] &gt; span:eq(1)&#x27;, &#x27;Jogo responsável&#x27;);\ncy.validateText(&#x27;[href=\&quot;https://www.estrelabet.bet.br/policy/sports-betting-rules\&quot;]&#x27;, &#x27;Regras de apostas esportivas&#x27;);\ncy.validateText(&#x27;[href=\&quot;https://www.estrelabet.bet.br/policy/bonus-rules\&quot;]&#x27;, &#x27;Termos e condições gerais de bônus&#x27;);\ncy.validateText(&#x27;[href=\&quot;https://www.estrelabet.bet.br/policy/privacy-policy\&quot;]&#x27;, &#x27;Política de privacidade&#x27;);\n// \&quot;Suporte\&quot;\n//cy.validateText(&#x27;.StaticFooter_footer-links-header__U2WL_&#x27;, &#x27;Suporte&#x27;)\ncy.contains(\&quot;span\&quot;, \&quot;Suporte\&quot;).should(\&quot;be.visible\&quot;);\ncy.validateText(&#x27;[href=\&quot;https://estrelabet.zendesk.com/hc\&quot;]&#x27;, &#x27;Central de ajuda&#x27;);\ncy.validateText(&#x27;[href=\&quot;tel:0800 000 4546\&quot;]&#x27;, &#x27;0800 000 4546&#x27;);\ncy.validateText(&#x27;[href=\&quot;mailto:<EMAIL>\&quot;]&#x27;, &#x27;<EMAIL>&#x27;);\n// \&quot;Outros\&quot;\n//cy.validateText(&#x27;.StaticFooter_footer-links-header__U2WL_&#x27;, &#x27;Outros&#x27;)\ncy.contains(\&quot;span\&quot;, \&quot;Outros\&quot;).should(\&quot;be.visible\&quot;);\ncy.validateText(&#x27;[href=\&quot;https://estrela-bet.atlassian.net/helpcenter/ouvidoria/\&quot;]&#x27;, &#x27;Ouvidoria&#x27;);\ncy.validateText(&#x27;[href=\&quot;https://consumidor2.procon.sp.gov.br/login\&quot;]&#x27;, &#x27;Procon&#x27;);\ncy.validateText(&#x27;[href=\&quot;https://www.planalto.gov.br/ccivil_03/leis/l8078compilado.htm\&quot;]&#x27;, &#x27;Código de Defesa do Consumidor&#x27;);\n// \&quot;Patrocinadores oficiais\&quot;\ncy.contains(&#x27;span&#x27;, &#x27;PATROCINADOR OFICIAL&#x27;).should(&#x27;be.visible&#x27;);\ncy.get(&#x27;img[alt=\&quot;Criciúma Esporte Clube\&quot;]&#x27;).should(&#x27;be.visible&#x27;);\ncy.get(&#x27;img[alt=\&quot;Villa Nova Atlético Clube\&quot;]&#x27;).should(&#x27;be.visible&#x27;);\ncy.get(&#x27;img[alt=\&quot;Vivo Keyd Stars\&quot;]&#x27;).should(&#x27;be.visible&#x27;);\n// \&quot;EstrelaBet Awards\&quot;\ncy.contains(&#x27;span&#x27;, &#x27;ESTRELABET AWARDS&#x27;).should(&#x27;be.visible&#x27;);\ncy.get(&#x27;img[alt=\&quot;Sigma Awards\&quot;]&#x27;).should(&#x27;be.visible&#x27;);\ncy.get(&#x27;img[alt=\&quot;BiS Awards\&quot;]&#x27;).should(&#x27;be.visible&#x27;);\ncy.get(&#x27;img[alt=\&quot;SBC Awards\&quot;]&#x27;).should(&#x27;be.visible&#x27;);\ncy.get(&#x27;img[alt=\&quot;CGS Awards\&quot;]&#x27;).should(&#x27;be.visible&#x27;);\n// \&quot;Aviso legal\&quot;\ncy.contains(&#x27;O jogo, se não controlado e feito com responsabilidade, pode ser prejudicial.&#x27;).should(&#x27;be.visible&#x27;);\ncy.contains(&#x27;a span&#x27;, &#x27;Jogo responsável.&#x27;).should(&#x27;be.visible&#x27;);\n// \&quot;Informações legais\&quot;\ncy.contains(&#x27;Autorização SPA/MF nº 320/2025&#x27;).should(&#x27;be.visible&#x27;);\ncy.contains(&#x27;Aposte com responsabilidade&#x27;).should(&#x27;be.visible&#x27;);\ncy.contains(&#x27;Aposta não é investimento&#x27;).should(&#x27;be.visible&#x27;);\ncy.contains(&#x27;Este site é operado por EB INTERMEDIAÇÕES E JOGOS S/A&#x27;).should(&#x27;be.visible&#x27;);\ncy.contains(&#x27;CNPJ sob o nº 52.639.845/0001-25&#x27;).should(&#x27;be.visible&#x27;);\ncy.get(&#x27;img[alt=\&quot;ibrj-instituto-brasileiro-jogo-responsavel\&quot;]&#x27;).should(&#x27;be.visible&#x27;);\ncy.get(&#x27;img[alt=\&quot;lexis-nexis\&quot;]&#x27;).should(&#x27;be.visible&#x27;);\ncy.get(&#x27;img[alt=\&quot;sportsradar\&quot;]&#x27;).should(&#x27;be.visible&#x27;);&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;124a4e01-9e43-4110-b1d9-f3246dc26733&quot;,&quot;parentUUID&quot;:&quot;92225da3-829e-443e-a9f3-cb5aaaeadeb4&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[&quot;124a4e01-9e43-4110-b1d9-f3246dc26733&quot;],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:6851,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000}],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:true,&quot;rootEmpty&quot;:true,&quot;_timeout&quot;:2000},{&quot;uuid&quot;:&quot;62e557ed-9084-434e-8aef-8679aeebae7e&quot;,&quot;title&quot;:&quot;&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\Backend\\login.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\Backend\\login.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[],&quot;suites&quot;:[{&quot;uuid&quot;:&quot;87a1a9f0-dcf8-4fbe-8656-a575daa1d0fe&quot;,&quot;title&quot;:&quot;Deve validar login&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\Backend\\login.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\Backend\\login.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Deve validar login com sucesso.&quot;,&quot;fullTitle&quot;:&quot;Deve validar login Deve validar login com sucesso.&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.request({\n  method: &#x27;POST&#x27;,\n  url: &#x27;https://cognito-idp.us-east-1.amazonaws.com/&#x27;,\n  headers: {\n    &#x27;content-type&#x27;: &#x27;application/x-amz-json-1.1&#x27;,\n    &#x27;x-amz-target&#x27;: &#x27;AWSCognitoIdentityProviderService.InitiateAuth&#x27;\n  },\n  body: {\n    AuthFlow: \&quot;USER_PASSWORD_AUTH\&quot;,\n    ClientId: \&quot;39freu20si9ss1tmsggcov5nrq\&quot;,\n    AuthParameters: {\n      USERNAME: Cypress.env(&#x27;user_name&#x27;),\n      PASSWORD: Cypress.env(&#x27;user_password&#x27;)\n    },\n    \&quot;ClientMetadata\&quot;: {}\n  }\n}).then(response =&gt; {\n  expect(response.status).to.eq(200);\n  expect(response.body.AuthenticationResult.AccessToken).to.not.empty;\n  expect(response.body.AuthenticationResult.ExpiresIn).to.eq(86400);\n  expect(response.body.AuthenticationResult.IdToken).to.not.empty;\n  expect(response.body.AuthenticationResult.RefreshToken).to.not.empty;\n  expect(response.body.AuthenticationResult.TokenType).to.eq(&#x27;Bearer&#x27;);\n  expect(response.body.ChallengeParameters).to.empty;\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;4aaf9ba1-e121-443e-a882-5beda9140f85&quot;,&quot;parentUUID&quot;:&quot;87a1a9f0-dcf8-4fbe-8656-a575daa1d0fe&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Deve validar login incorreto&quot;,&quot;fullTitle&quot;:&quot;Deve validar login Deve validar login incorreto&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.request({\n  method: &#x27;POST&#x27;,\n  url: &#x27;https://cognito-idp.us-east-1.amazonaws.com/&#x27;,\n  headers: {\n    &#x27;content-type&#x27;: &#x27;application/x-amz-json-1.1&#x27;,\n    &#x27;x-amz-target&#x27;: &#x27;AWSCognitoIdentityProviderService.InitiateAuth&#x27;\n  },\n  body: {\n    AuthFlow: \&quot;USER_PASSWORD_AUTH\&quot;,\n    ClientId: \&quot;3103ticv585k9u5mha4c6ckdq9\&quot;,\n    AuthParameters: {\n      USERNAME: Cypress.env(&#x27;user_name&#x27;),\n      PASSWORD: &#x27;123321&#x27;\n    },\n    \&quot;ClientMetadata\&quot;: {}\n  },\n  failOnStatusCode: false\n}).then(response =&gt; {\n  expect(response.status).to.eq(400);\n  expect(response.body.__type).to.eq(&#x27;NotAuthorizedException&#x27;);\n  expect(response.body.message).to.eq(&#x27;Incorrect username or password.&#x27;);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;fe6bb6c3-240d-420f-b0a8-cb9c1fe6e0cf&quot;,&quot;parentUUID&quot;:&quot;87a1a9f0-dcf8-4fbe-8656-a575daa1d0fe&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[&quot;4aaf9ba1-e121-443e-a882-5beda9140f85&quot;,&quot;fe6bb6c3-240d-420f-b0a8-cb9c1fe6e0cf&quot;],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000}],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:true,&quot;rootEmpty&quot;:true,&quot;_timeout&quot;:2000},{&quot;uuid&quot;:&quot;a5b7be16-785d-4031-8a82-ab326e7c5dfe&quot;,&quot;title&quot;:&quot;&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\LoginLogout.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\LoginLogout.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[],&quot;suites&quot;:[{&quot;uuid&quot;:&quot;951db92c-b741-4ead-95cf-13aae9bbc941&quot;,&quot;title&quot;:&quot;Deve validar o login&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\LoginLogout.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\LoginLogout.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Validando elementos da tela&quot;,&quot;fullTitle&quot;:&quot;Deve validar o login Validando elementos da tela&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:10638,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;slow&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;context&quot;:null,&quot;code&quot;:&quot;//Suporte\ncy.validateText(&#x27;span.d_block&#x27;, &#x27;Suporte&#x27;);\n//Valida logo do cliente\ncy.get(_locators.default.LOGIN.LOGO_CLIENTE).should(&#x27;be.visible&#x27;).and(&#x27;have.attr&#x27;, &#x27;src&#x27;).and(&#x27;include&#x27;, &#x27;/estrelabet/logo&#x27;);\n//Validação do título e subtítulo\ncy.validateTextContains(_validationMessages.default.VALIDATIONS.LOGIN_PAGE.TITLE);\n//Validando campo email, título e placeholder\ncy.validateText(_locators.default.LOGIN.EMAIL_TITULO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.EMAIL_TITULO);\ncy.validatePlaceholder(_locators.default.LOGIN.EMAIL_CAMPO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.PLACEHOLDER_EMAIL);\n//Validando campo senha, título e placeholder\ncy.validateText(_locators.default.LOGIN.SENHA_TITULO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.SENHA_TITULO);\ncy.validatePlaceholder(_locators.default.LOGIN.SENHA_CAMPO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.PLACEHOLDER_SENHA);\n// Digitar senha\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).type(&#x27;123456&#x27;).should(&#x27;have.attr&#x27;, &#x27;type&#x27;, &#x27;password&#x27;); // Verifica que está oculta\n// Clicar no botão de mostrar senha\ncy.get(_locators.default.LOGIN.ICONE_OLHO_SENHA).should(&#x27;be.visible&#x27;).and(&#x27;exist&#x27;).click();\n// Verificar que a senha está visível\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).should(&#x27;have.attr&#x27;, &#x27;type&#x27;, &#x27;text&#x27;);\n// Clicar novamente para ocultar senha\ncy.get(_locators.default.LOGIN.ICONE_OLHO_SENHA).click();\n// Verificar que a senha voltou a ser oculta\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).should(&#x27;have.attr&#x27;, &#x27;type&#x27;, &#x27;password&#x27;);\n//Botão esqueci a senha\ncy.validateTextContains(_validationMessages.default.VALIDATIONS.RECUPERAR_SENHA.BOTÃO_RECUPERAR_SENHA);\n//Botão entrar desabilitado\ncy.get(_locators.default.LOGIN.BOTAO_ENTRAR).should(&#x27;have.text&#x27;, &#x27;Entrar&#x27;).and(&#x27;be.visible&#x27;).and(&#x27;be.disabled&#x27;);&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;272e8bc8-d171-4e9e-85e9-67778333e206&quot;,&quot;parentUUID&quot;:&quot;951db92c-b741-4ead-95cf-13aae9bbc941&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Deve realizar o login com sucesso&quot;,&quot;fullTitle&quot;:&quot;Deve validar o login Deve realizar o login com sucesso&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:18131,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;slow&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;context&quot;:null,&quot;code&quot;:&quot;const username = Cypress.env(&#x27;user_name&#x27;);\nconst password = Cypress.env(&#x27;user_password&#x27;);\nexpect(username, &#x27;Nome de usuário&#x27;).to.be.a(&#x27;string&#x27;).and.not.be.empty;\nif (typeof password !== &#x27;string&#x27; || !password) {\n  throw new Error(&#x27;O valor senha está ausente, inclua a senha usando o cy.env&#x27;);\n}\ncy.get(_locators.default.LOGIN.EMAIL_CAMPO).type(username).should(&#x27;have.value&#x27;, username);\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).type(password, {\n  log: false\n}).should(el$ =&gt; {\n  if (el$.val() !== password) {\n    throw new Error(&#x27;Valor diferente da senha digitada&#x27;);\n  }\n});\ncy.clickMouse(_locators.default.LOGIN.BOTAO_ENTRAR);\ncy.wait(&#x27;@login&#x27;).its(&#x27;response.statusCode&#x27;).should(&#x27;eq&#x27;, 200);\ncy.get(&#x27;@login&#x27;).then(({\n  request,\n  response\n}) =&gt; {\n  expect(request.method).to.equal(&#x27;POST&#x27;);\n  cy.isVisible(&#x27;.nebulosa-avatar__fallback:eq(2)&#x27;);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;2ce13392-8e31-44df-926f-002272a09990&quot;,&quot;parentUUID&quot;:&quot;951db92c-b741-4ead-95cf-13aae9bbc941&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[{&quot;uuid&quot;:&quot;a40b454b-**************-be27e6670445&quot;,&quot;title&quot;:&quot;Deve validar padrões de email incorreto&quot;,&quot;fullFile&quot;:&quot;&quot;,&quot;file&quot;:&quot;&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Deve validar login credenciais incorretas&quot;,&quot;fullTitle&quot;:&quot;Deve validar o login Deve validar padrões de email incorreto Deve validar login credenciais incorretas&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:22204,&quot;state&quot;:&quot;failed&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:true,&quot;pending&quot;:false,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.typeText(_locators.default.LOGIN.EMAIL_CAMPO, &#x27;<EMAIL>&#x27;);\ncy.typeText(_locators.default.LOGIN.SENHA_CAMPO, &#x27;123456Uds@&#x27;);\ncy.clickMouse(_locators.default.LOGIN.BOTAO_ENTRAR);\ncy.wait(&#x27;@login&#x27;).its(&#x27;response.statusCode&#x27;).should(&#x27;eq&#x27;, 401);\ncy.validateTextContains(_validationMessages.default.FRONT_ERRORS.LOGIN_PAGE.LOGIN_INVALIDO);&quot;,&quot;err&quot;:{&quot;message&quot;:&quot;AssertionError: Timed out retrying after 8000ms: expected 307 to equal 401&quot;,&quot;estack&quot;:&quot;AssertionError: Timed out retrying after 8000ms: expected 307 to equal 401\n    at Context.eval (webpack://EstrelaBet/./cypress/e2e/FrontEnd/LoginLogout.js:93:57)&quot;,&quot;diff&quot;:&quot;- 307\n+ 401\n&quot;},&quot;uuid&quot;:&quot;324be4e8-112d-4fed-8b8c-794b6cab2170&quot;,&quot;parentUUID&quot;:&quot;a40b454b-**************-be27e6670445&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Deve validar login nome do usuario incorreto&quot;,&quot;fullTitle&quot;:&quot;Deve validar o login Deve validar padrões de email incorreto Deve validar login nome do usuario incorreto&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:26987,&quot;state&quot;:&quot;failed&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:true,&quot;pending&quot;:false,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.typeText(_locators.default.LOGIN.EMAIL_CAMPO, &#x27;teste&#x27;);\ncy.typeText(_locators.default.LOGIN.SENHA_CAMPO, &#x27;123456Uds@&#x27;);\ncy.clickMouse(_locators.default.LOGIN.BOTAO_ENTRAR);\ncy.wait(&#x27;@login&#x27;).its(&#x27;response.statusCode&#x27;).should(&#x27;eq&#x27;, 400);\ncy.validateTextContains(_validationMessages.default.FRONT_ERRORS.LOGIN_PAGE.LOGIN_NOME_USUARIO_INVALIDO);&quot;,&quot;err&quot;:{&quot;message&quot;:&quot;AssertionError: Timed out retrying after 8000ms: expected 403 to equal 400&quot;,&quot;estack&quot;:&quot;AssertionError: Timed out retrying after 8000ms: expected 403 to equal 400\n    at Context.eval (webpack://EstrelaBet/./cypress/e2e/FrontEnd/LoginLogout.js:100:57)&quot;,&quot;diff&quot;:&quot;- 403\n+ 400\n&quot;},&quot;uuid&quot;:&quot;a5b4f5c5-8d94-41e0-b112-d25d22388335&quot;,&quot;parentUUID&quot;:&quot;a40b454b-**************-be27e6670445&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Deve validar qtd mínima de caracteres no email&quot;,&quot;fullTitle&quot;:&quot;Deve validar o login Deve validar padrões de email incorreto Deve validar qtd mínima de caracteres no email&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:3999,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;fast&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.typeText(_locators.default.LOGIN.EMAIL_CAMPO, &#x27;qa&#x27;);\ncy.get(_locators.default.MENSAGENS_ERRO.EMAIL_INVÁLIDO).should(&#x27;have.text&#x27;, _validationMessages.default.FRONT_ERRORS.LOGIN_PAGE.EMAIL_DOIS_CARACTERES);\n//Botão entrar desabilitado\ncy.get(_locators.default.LOGIN.BOTAO_ENTRAR).should(&#x27;have.text&#x27;, &#x27;Entrar&#x27;).and(&#x27;be.visible&#x27;).and(&#x27;be.disabled&#x27;);&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;5069ada2-7627-4791-bfb1-93cfb7f39664&quot;,&quot;parentUUID&quot;:&quot;a40b454b-**************-be27e6670445&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Deve validar qtd mínima de caracteres na senha&quot;,&quot;fullTitle&quot;:&quot;Deve validar o login Deve validar padrões de email incorreto Deve validar qtd mínima de caracteres na senha&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:6503,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;medium&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.typeText(_locators.default.LOGIN.SENHA_CAMPO, &#x27;1234567&#x27;);\ncy.get(_locators.default.MENSAGENS_ERRO.COMPLEXIDADE_SENHA_INCORRETA).should(&#x27;have.text&#x27;, _validationMessages.default.FRONT_ERRORS.LOGIN_PAGE.SENHA_QTD_INCORRETA);\n//Botão entrar desabilitado\ncy.get(_locators.default.LOGIN.BOTAO_ENTRAR).should(&#x27;have.text&#x27;, &#x27;Entrar&#x27;).and(&#x27;be.visible&#x27;).and(&#x27;be.disabled&#x27;);&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;458b617c-79a5-4ff8-a33e-11b0661222a9&quot;,&quot;parentUUID&quot;:&quot;a40b454b-**************-be27e6670445&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[&quot;5069ada2-7627-4791-bfb1-93cfb7f39664&quot;,&quot;458b617c-79a5-4ff8-a33e-11b0661222a9&quot;],&quot;failures&quot;:[&quot;324be4e8-112d-4fed-8b8c-794b6cab2170&quot;,&quot;a5b4f5c5-8d94-41e0-b112-d25d22388335&quot;],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:59693,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000}],&quot;passes&quot;:[&quot;272e8bc8-d171-4e9e-85e9-67778333e206&quot;,&quot;2ce13392-8e31-44df-926f-002272a09990&quot;],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:28769,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000},{&quot;uuid&quot;:&quot;aeeaf6f7-21f6-4bc8-93a3-be6224ae61c7&quot;,&quot;title&quot;:&quot;Deve validar o logout&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\LoginLogout.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\LoginLogout.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Deve realizar o logout&quot;,&quot;fullTitle&quot;:&quot;Deve validar o logout Deve realizar o logout&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:36031,&quot;state&quot;:&quot;failed&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:true,&quot;pending&quot;:false,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.get(&#x27;.nebulosa-header__desktopButtonWrapper &gt; .nebulosa-avatar__avatarContainer &gt; .nebulosa-avatar__root &gt; .nebulosa-avatar__fallback&#x27;).should(&#x27;be.visible&#x27;).click();\n//Clicando em sair\ncy.contains(&#x27;section.d_flex &gt; div&#x27;, &#x27;Sair&#x27;).should(&#x27;be.visible&#x27;).click();\ncy.wait(&#x27;@logout&#x27;).its(&#x27;response.statusCode&#x27;).should(&#x27;eq&#x27;, 200);&quot;,&quot;err&quot;:{&quot;message&quot;:&quot;CypressError: Timed out retrying after 10000ms: `cy.wait()` timed out waiting `10000ms` for the 1st request to the route: `logout`. No request ever occurred.\n\nhttps://on.cypress.io/wait&quot;,&quot;estack&quot;:&quot;CypressError: Timed out retrying after 10000ms: `cy.wait()` timed out waiting `10000ms` for the 1st request to the route: `logout`. No request ever occurred.\n\nhttps://on.cypress.io/wait\n    at cypressErr (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:77664:18)\n    at Object.errByPath (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:77718:10)\n    at checkForXhr (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:138745:84)\n    at &lt;unknown&gt; (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:138771:28)\n    at tryCatcher (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:1777:23)\n    at Promise.attempt.Promise.try (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:4285:29)\n    at whenStable (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:147181:68)\n    at &lt;unknown&gt; (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:147122:14)\n    at tryCatcher (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:1777:23)\n    at Promise._settlePromiseFromHandler (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:1489:31)\n    at Promise._settlePromise (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:1546:18)\n    at Promise._settlePromise0 (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:1591:10)\n    at Promise._settlePromises (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:1671:18)\n    at Promise._fulfill (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:1615:18)\n    at &lt;unknown&gt; (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:5420:46)&quot;,&quot;diff&quot;:null},&quot;uuid&quot;:&quot;077f1501-373f-4096-8801-2b7ba736c30e&quot;,&quot;parentUUID&quot;:&quot;aeeaf6f7-21f6-4bc8-93a3-be6224ae61c7&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[],&quot;failures&quot;:[&quot;077f1501-373f-4096-8801-2b7ba736c30e&quot;],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:36031,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000}],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:true,&quot;rootEmpty&quot;:true,&quot;_timeout&quot;:2000},{&quot;uuid&quot;:&quot;b83ed401-fc77-4a60-b80c-997f445a0750&quot;,&quot;title&quot;:&quot;&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\minhaConta.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\minhaConta.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[],&quot;suites&quot;:[{&quot;uuid&quot;:&quot;e66c64fe-5e9f-450f-aea5-37b979de76fb&quot;,&quot;title&quot;:&quot;Deve validar opções da conta&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\minhaConta.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\minhaConta.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Validando pagina da minha conta&quot;,&quot;fullTitle&quot;:&quot;Deve validar opções da conta Validando pagina da minha conta&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:11731,&quot;state&quot;:&quot;failed&quot;,&quot;speed&quot;:null,&quot;pass&quot;:false,&quot;fail&quot;:true,&quot;pending&quot;:false,&quot;context&quot;:null,&quot;code&quot;:&quot;//Validando avatar\ncy.isVisible(&#x27;.nebulosa-header__desktopButtonWrapper &gt; .nebulosa-avatar__avatarContainer &gt; .nebulosa-avatar__root &gt; .nebulosa-avatar__fallback&#x27;);\n//Abrindo opções do usuário\ncy.clickMouse(&#x27;.nebulosa-header__desktopButtonWrapper &gt; .nebulosa-avatar__avatarContainer &gt; .nebulosa-avatar__root &gt; .nebulosa-avatar__fallback&#x27;);\n//Validando opção Minha conta e acessando\ncy.validateText(&#x27;:nth-child(4) &gt; :nth-child(2) &gt; .fw_regular&#x27;, &#x27;Minha conta&#x27;);\ncy.clickMouse(&#x27;:nth-child(4) &gt; :nth-child(2) &gt; .fw_regular&#x27;);\n//validando opções de acesso do usuário\ncy.get(&#x27;[role=\&quot;menuitem\&quot;] &gt; label:eq(0)&#x27;).should(&#x27;have.text&#x27;, &#x27;Conta&#x27;).and(&#x27;be.visible&#x27;);\ncy.get(&#x27;[role=\&quot;menuitem\&quot;] &gt; label:eq(1)&#x27;).should(&#x27;have.text&#x27;, &#x27;Carteira&#x27;).and(&#x27;be.visible&#x27;);\ncy.get(&#x27;[role=\&quot;menuitem\&quot;] &gt; label:eq(2)&#x27;).should(&#x27;have.text&#x27;, &#x27;Bônus&#x27;).and(&#x27;be.visible&#x27;);\ncy.get(&#x27;[role=\&quot;menuitem\&quot;] &gt; label:eq(3)&#x27;).should(&#x27;have.text&#x27;, &#x27;Apostas&#x27;).and(&#x27;be.visible&#x27;);\ncy.get(&#x27;[role=\&quot;menuitem\&quot;] &gt; label:eq(4)&#x27;).should(&#x27;have.text&#x27;, &#x27;Limites&#x27;).and(&#x27;be.visible&#x27;);\ncy.get(&#x27;[role=\&quot;menuitem\&quot;] &gt; label:eq(5)&#x27;).should(&#x27;have.text&#x27;, &#x27;Segurança&#x27;).and(&#x27;be.visible&#x27;);\ncy.log(&#x27;Validando modal Minha conta&#x27;);\ncy.validateText(&#x27;.fs_lg&#x27;, &#x27;Minha conta&#x27;);\ncy.validateText(&#x27;.mb_md &gt; .fs_md&#x27;, &#x27;Meu perfil&#x27;);\ncy.contains(&#x27;Dados Pessoais&#x27;).should(&#x27;be.visible&#x27;);\ncy.validateTextContains(&#x27;Ver dados pessoais&#x27;);\ncy.validateText(&#x27;button &gt; :nth-child(3):eq(0)&#x27;, &#x27;Visualizar&#x27;);\ncy.validateText(&#x27;.nebulosa-badge__root--type_success &gt; label:eq(0)&#x27;, &#x27;Completo&#x27;);\ncy.contains(&#x27;E-mail&#x27;).should(&#x27;be.visible&#x27;);\ncy.isVisible(&#x27;[data-webview-id=\&quot;HidenElement\&quot;] &gt; .d_flex &gt; div &gt; .lh_lg:eq(1)&#x27;);\ncy.validateText(&#x27;button &gt; :nth-child(3):eq(1)&#x27;, &#x27;Editar&#x27;);\ncy.validateText(&#x27;.nebulosa-badge__root--type_success &gt; label:eq(1)&#x27;, &#x27;Completo&#x27;);\ncy.contains(&#x27;Telefone&#x27;).should(&#x27;be.visible&#x27;);\ncy.isVisible(&#x27;[data-webview-id=\&quot;HidenElement\&quot;] &gt; .d_flex &gt; div &gt; .lh_lg:eq(2)&#x27;);\ncy.validateText(&#x27;button &gt; :nth-child(3):eq(2)&#x27;, &#x27;Editar&#x27;);\ncy.validateText(&#x27;.nebulosa-badge__root--type_success &gt; label:eq(2)&#x27;, &#x27;Completo&#x27;);\ncy.contains(&#x27;Endereço&#x27;).should(&#x27;be.visible&#x27;);\ncy.isVisible(&#x27;[data-webview-id=\&quot;HidenElement\&quot;] &gt; .d_flex &gt; div &gt; .lh_lg:eq(3)&#x27;);\ncy.validateText(&#x27;button &gt; :nth-child(3):eq(3)&#x27;, &#x27;Editar&#x27;);\ncy.validateText(&#x27;.nebulosa-badge__root--type_success &gt; label:eq(3)&#x27;, &#x27;Completo&#x27;);\ncy.log(&#x27;Validando modal alterar senha&#x27;);\ncy.validateText(&#x27;.mb_sm &gt; h2:eq(0)&#x27;, &#x27;Alterar senha&#x27;);\ncy.validateText(&#x27;.mb_lg &gt; div &gt; div &gt; label:eq(0)&#x27;, &#x27;Senha atual&#x27;);\ncy.validateText(&#x27;.mb_lg &gt; div &gt; div &gt; label:eq(1)&#x27;, &#x27;Criar nova senha&#x27;);\ncy.validateText(&#x27;.mb_lg &gt; div &gt; div &gt; label:eq(2)&#x27;, &#x27;Confirme a nova senha&#x27;);\ncy.get(&#x27;[type=\&quot;submit\&quot;]:eq(0)&#x27;).should(&#x27;have.text&#x27;, &#x27;Salvar&#x27;).and(&#x27;be.disabled&#x27;);\ncy.log(&#x27;Validando modal preferencia de comunicação&#x27;);\ncy.get(&#x27;[type=\&quot;submit\&quot;]:eq(1)&#x27;).should(&#x27;have.text&#x27;, &#x27;Salvar&#x27;).and(&#x27;be.disabled&#x27;);\ncy.validateText(&#x27;.mb_sm &gt; h2:eq(1)&#x27;, &#x27;Preferências de comunicação&#x27;);\ncy.validateText(&#x27;div.my_md&#x27;, &#x27;Quero receber notificações relacionadas às minhas solicitações de depósito/saque, apostas grátis, ofertas e comunicações de marketing personalizadas da EstrelaBet por:&#x27;);\n//Opção checkbox\ncy.validateText(&#x27;[for=\&quot;emailSubscribed\&quot;]&#x27;, &#x27;E-mail&#x27;);\ncy.validateText(&#x27;[for=\&quot;mobileSubscribed\&quot;]&#x27;, &#x27;SMS&#x27;);\n//email\ncy.get(&#x27;[role=\&quot;checkbox\&quot;]:eq(0)&#x27;).should(&#x27;have.attr&#x27;, &#x27;aria-checked&#x27;, &#x27;true&#x27;);\ncy.clickMouse(&#x27;[role=\&quot;checkbox\&quot;]:eq(0)&#x27;);\ncy.get(&#x27;[role=\&quot;checkbox\&quot;]:eq(0)&#x27;).should(&#x27;have.attr&#x27;, &#x27;aria-checked&#x27;, &#x27;false&#x27;);\ncy.clickMouse(&#x27;[role=\&quot;checkbox\&quot;]:eq(0)&#x27;);\ncy.get(&#x27;[role=\&quot;checkbox\&quot;]:eq(0)&#x27;).should(&#x27;have.attr&#x27;, &#x27;aria-checked&#x27;, &#x27;true&#x27;);\n//sms\ncy.get(&#x27;[role=\&quot;checkbox\&quot;]:eq(1)&#x27;).should(&#x27;have.attr&#x27;, &#x27;aria-checked&#x27;, &#x27;true&#x27;);\ncy.clickMouse(&#x27;[role=\&quot;checkbox\&quot;]:eq(1)&#x27;);\ncy.get(&#x27;[role=\&quot;checkbox\&quot;]:eq(1)&#x27;).should(&#x27;have.attr&#x27;, &#x27;aria-checked&#x27;, &#x27;false&#x27;);\ncy.clickMouse(&#x27;[role=\&quot;checkbox\&quot;]:eq(1)&#x27;);\ncy.get(&#x27;[role=\&quot;checkbox\&quot;]:eq(1)&#x27;).should(&#x27;have.attr&#x27;, &#x27;aria-checked&#x27;, &#x27;true&#x27;);&quot;,&quot;err&quot;:{&quot;message&quot;:&quot;AssertionError: Timed out retrying after 4000ms: Expected to find content: &#x27;Aceitar todos os cookies&#x27; within the element: &lt;button.nebulosa-button__root.nebulosa-button__root--size_Large.nebulosa-button__root--variant_primary.nebulosa-button__root--loading_true.nebulosa-button__root--disabled_true&gt; but never did.\n\nThis error occurred while creating the session. Because the session setup failed, we failed the test.\n\nBecause this error occurred during a `before each` hook we are skipping the remaining tests in the current suite: `Deve validar opções da conta`&quot;,&quot;estack&quot;:&quot;AssertionError: Timed out retrying after 4000ms: Expected to find content: &#x27;Aceitar todos os cookies&#x27; within the element: &lt;button.nebulosa-button__root.nebulosa-button__root--size_Large.nebulosa-button__root--variant_primary.nebulosa-button__root--loading_true.nebulosa-button__root--disabled_true&gt; but never did.\n\nThis error occurred while creating the session. Because the session setup failed, we failed the test.\n\nBecause this error occurred during a `before each` hook we are skipping the remaining tests in the current suite: `Deve validar opções da conta`\n    at Object.eval [as setup] (webpack://EstrelaBet/./cypress/support/commands.js:201:17)\n    at Context.&lt;anonymous&gt; (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:136486:38)\n    at getRet (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:121549:20)\n    at tryCatcher (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:1777:23)\n    at Promise.attempt.Promise.try (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:4285:29)\n    at Context.thenFn (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:121560:66)\n    at Context.then (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:121811:21)\n    at wrapped (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:141517:19)&quot;,&quot;diff&quot;:null},&quot;uuid&quot;:&quot;fadf5652-5d2b-458b-b39c-89debdc39ec6&quot;,&quot;parentUUID&quot;:&quot;e66c64fe-5e9f-450f-aea5-37b979de76fb&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[],&quot;failures&quot;:[&quot;fadf5652-5d2b-458b-b39c-89debdc39ec6&quot;],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:11731,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000},{&quot;uuid&quot;:&quot;e5c48a7d-6546-40ce-bfea-df53749819a7&quot;,&quot;title&quot;:&quot;Deve validar carteira&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\minhaConta.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\minhaConta.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Deve validar pagina minha carteira&quot;,&quot;fullTitle&quot;:&quot;Deve validar carteira Deve validar pagina minha carteira&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:24862,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;slow&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.log(&#x27;Validando área de saldo&#x27;);\n//Título\ncy.validateText(&#x27;h1&#x27;, &#x27;Carteira&#x27;);\n//Validando textos e componentes da area de saldo\ncy.validateText(_locators.default.CARTEIRA.SALDO_TOTAL, &#x27;Saldo total&#x27;);\ncy.get(&#x27;p.fs_lg&#x27;).should(&#x27;contain.text&#x27;, &#x27;R$&#x27;);\n//Saldo livre para saque\ncy.validateText(_locators.default.CARTEIRA.SALDO_LIVRE_PARA_SAQUE, &#x27;Saldo livre para saque&#x27;);\ncy.get(&#x27;.d_flex &gt; p.fs_sm:eq(2)&#x27;).should(&#x27;contain.text&#x27;, &#x27;R$&#x27;);\n//Bônus esportes\ncy.validateText(_locators.default.CARTEIRA.BONUS_ESPORTES, &#x27;Bônus esportes&#x27;);\ncy.get(&#x27;div.flex-d_row &gt; .flex-d_column &gt; p.fs_xs:eq(1)&#x27;).should(&#x27;contain.text&#x27;, &#x27;R$&#x27;);\n//Bonus cassino\ncy.validateText(_locators.default.CARTEIRA.BONUS_CASSINO, &#x27;Bônus cassino&#x27;);\ncy.get(&#x27;div.flex-d_row &gt; .flex-d_column &gt; p.fs_xs:eq(2)&#x27;).should(&#x27;contain.text&#x27;, &#x27;R$&#x27;);\n//Saldo restrito\ncy.validateText(_locators.default.CARTEIRA.SALDO_RESTRITO, &#x27;Saldo restrito&#x27;);\ncy.get(&#x27;div.flex-d_row &gt; .flex-d_column &gt; p.fs_xs:eq(3)&#x27;).should(&#x27;contain.text&#x27;, &#x27;R$&#x27;);\n//botões de ações\ncy.validateText(_locators.default.CARTEIRA.BOTAO_DEPOSITAR, &#x27;Depositar&#x27;);\ncy.validateText(_locators.default.CARTEIRA.BOTAO_SACAR, &#x27;Sacar&#x27;);\ncy.log(&#x27;Validando área das minhas transações&#x27;);\n//Titulo\ncy.validateText(&#x27;h2:eq(1)&#x27;, &#x27;Minhas transações&#x27;);\ncy.contains(&#x27;button &gt; span&#x27;, &#x27;Ver minhas transações&#x27;).should(&#x27;be.visible&#x27;).click();\n//Validando título do periodo de e até\ncy.validateText(&#x27;.d_flex &gt; label:eq(1)&#x27;, &#x27;Período de&#x27;);\ncy.validateText(&#x27;.d_flex &gt; label:eq(2)&#x27;, &#x27;Período até&#x27;);\n//Tipo de transação\ncy.validateText(&#x27;.nebulosa-select__Wrapper &gt; label&#x27;, &#x27;Tipo de transação&#x27;);\ncy.clickMouse(&#x27;[role=\&quot;combobox\&quot;]:eq(0)&#x27;);\nconst tipoTransacao = [&#x27;Depósito&#x27;, &#x27;Saque cancelado&#x27;, &#x27;Saque&#x27;, &#x27;Ajuste de saldo positivo&#x27;, &#x27;Ajuste de saldo negativo&#x27;, &#x27;Todos/Todas&#x27;];\ncy.get(&#x27;[role=\&quot;option\&quot;] &gt; span&#x27;).should(&#x27;have.length&#x27;, 6).then($items =&gt; {\n  const firstSix = $items.slice(0, 11);\n  firstSix.each((index, item) =&gt; {\n    expect(Cypress.$(item).text()).to.contain(tipoTransacao[index]);\n  });\n});\ncy.get(&#x27;body&#x27;).type(&#x27;{esc}&#x27;); //Fechando o select\n//Botão filtrar\ncy.validateText(&#x27;[type=\&quot;submit\&quot;]&#x27;, &#x27;Filtrar&#x27;);\n//Validando título das colunas da tabela\ncy.validateText(&#x27;th:eq(0)&#x27;, &#x27;Status&#x27;);\ncy.validateText(&#x27;th:eq(1)&#x27;, &#x27;Data e hora&#x27;);\ncy.validateText(&#x27;th:eq(2)&#x27;, &#x27;Detalhe da transação&#x27;);\ncy.validateText(&#x27;th:eq(3)&#x27;, &#x27;Transação&#x27;);\ncy.validateText(&#x27;th:eq(4)&#x27;, &#x27;Saldo anterior&#x27;);\ncy.validateText(&#x27;th:eq(5)&#x27;, &#x27;Valor da transação&#x27;);\ncy.validateText(&#x27;th:eq(6)&#x27;, &#x27;Saldo final&#x27;);\ncy.validateText(&#x27;td &gt; p&#x27;, &#x27;Nenhuma transação disponível&#x27;);\n//Botão solicitar histórico\ncy.validateText(&#x27;[lefticon=\&quot;utility-square-list-solid\&quot;]&#x27;, &#x27;Solicitar histórico de 36 meses&#x27;);\ncy.log(&#x27;Validando modal dos dados bancários&#x27;);\n//Título\ncy.validateText(&#x27;section &gt; h3&#x27;, &#x27;Dados bancários&#x27;);\ncy.validateText(&#x27;section &gt; p&#x27;, &#x27;Certifique-se de que as chaves Pix e conta bancária esteja vinculada ao seu CPF.&#x27;);\n//Informações da conta\ncy.validateText(&#x27;article &gt; .fw_bold&#x27;, &#x27;Chave(s) Pix&#x27;);\ncy.isVisible(&#x27;li &gt; span:eq(0)&#x27;);\ncy.isVisible(&#x27;li &gt; span:eq(1)&#x27;);\ncy.isVisible(&#x27;li &gt; span:eq(2)&#x27;);\ncy.validateText(&#x27;.gap_xs &gt; .gap_md &gt; .nebulosa-button__root &gt; .nebulosa-button__buttonLabel&#x27;, &#x27;Alterar dados&#x27;);\ncy.log(&#x27;Validando área de informe de rendimento&#x27;);\ncy.validateText(&#x27;.gap_xxxs &gt; h3:eq(1)&#x27;, &#x27;Informe de rendimento&#x27;);\ncy.validateText(&#x27;h4&#x27;, &#x27;Solicite para o seu imposto de renda&#x27;);\ncy.contains(&#x27;div.d_flex &gt; .fs_xs&#x27;, &#x27;Ano fiscal&#x27;).should(&#x27;be.visible&#x27;);\n//Componente de ano fiscal \ncy.isVisible(&#x27;:nth-child(1) &gt; .nebulosa-select__Wrapper&#x27;);\ncy.get(&#x27;.d_flex &gt; div &gt; .flex-d_column &gt; button:eq(1)&#x27;).should(&#x27;have.text&#x27;, &#x27;Solicitar informe&#x27;).and(&#x27;be.disabled&#x27;);\ncy.contains(&#x27;.fs_xxs&#x27;, &#x27;Em breve disponível. Fique ligado!&#x27;).should(&#x27;be.visible&#x27;);&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;d338ecd3-4e28-4909-9da4-12ff42b8d9d5&quot;,&quot;parentUUID&quot;:&quot;e5c48a7d-6546-40ce-bfea-df53749819a7&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[&quot;d338ecd3-4e28-4909-9da4-12ff42b8d9d5&quot;],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:24862,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000}],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:true,&quot;rootEmpty&quot;:true,&quot;_timeout&quot;:2000},{&quot;uuid&quot;:&quot;19a5f649-dc79-4fed-b04c-afeb99a615a9&quot;,&quot;title&quot;:&quot;&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\Saque.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\Saque.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[],&quot;suites&quot;:[{&quot;uuid&quot;:&quot;e9c5afdb-d426-43b0-8df3-3161154c64f3&quot;,&quot;title&quot;:&quot;Cadastro&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\Saque.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\Saque.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Deve validar o botão de saque&quot;,&quot;fullTitle&quot;:&quot;Cadastro Deve validar o botão de saque&quot;,&quot;timedOut&quot;:null,&quot;duration&quot;:30290,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;slow&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;context&quot;:null,&quot;code&quot;:&quot;cy.get(&#x27;.nebulosa-header__desktopButtonWrapper &gt; .nebulosa-avatar__avatarContainer &gt; .nebulosa-avatar__root &gt; .nebulosa-avatar__fallback&#x27;).should(&#x27;be.visible&#x27;).click();\ncy.isVisible(&#x27;button&#x27;, &#x27;Sacar&#x27;);\ncy.clickByContains(&#x27;button&#x27;, &#x27;Sacar&#x27;);\ncy.get(&#x27;input[name=\&quot;amount\&quot;]&#x27;).type(&#x27;10&#x27;);\ncy.isVisible(&#x27;button&#x27;, &#x27;Sacar&#x27;);\ncy.clickMouse(&#x27;button[type=\&quot;submit\&quot;] span&#x27;, &#x27;Sacar&#x27;);\nconst sessionId = Cypress.env(&#x27;session_id&#x27;);\ncy.api({\n  method: &#x27;GET&#x27;,\n  url: urlBff + &#x27;/bonus/active&#x27;,\n  failOnStatusCode: false,\n  headers: {\n    sessionid: `${sessionId}`\n  }\n}).then(response =&gt; {\n  if (response.status === 200) {\n    cy.log(&#x27;✅ /bonus/active retornou 200&#x27;);\n  } else {\n    cy.log(`⚠️ /bonus/active retornou ${response.status}`);\n  }\n  cy.log(`📦 Body: ${JSON.stringify(response.body).substring(0, 300)}...`);\n  expect(response.status).to.be.oneOf([200, 400]);\n});\ncy.api({\n  method: &#x27;GET&#x27;,\n  url: urlBff + &#x27;/cashier/instruments/details&#x27;,\n  failOnStatusCode: false,\n  headers: {\n    sessionid: `${sessionId}`\n  }\n}).then(response =&gt; {\n  if (response.status === 200) {\n    cy.log(&#x27;✅ /cashier/instruments/details retornou 200&#x27;);\n  } else {\n    cy.log(`⚠️ /cashier/instruments/details retornou ${response.status}`);\n  }\n  cy.log(`📦 Body: ${JSON.stringify(response.body).substring(0, 300)}...`);\n  expect(response.status).to.be.oneOf([200, 400]);\n});\nconst payload = {\n  depositAmount: 1,\n  paymentMethod: \&quot;PIXP2F\&quot;,\n  instrumentId: 6654566\n  //lnSessionId: sessionId, e outro idsession\n};\ncy.api({\n  method: &#x27;POST&#x27;,\n  url: urlBff + &#x27;/cashier/wallet-withdrawal&#x27;,\n  body: payload,\n  failOnStatusCode: false\n}).then(response =&gt; {\n  if (response.status === 200) {\n    cy.log(&#x27;✅ Requisição bem-sucedida: &#x27; + response.status);\n  } else if (response.status === 400) {\n    cy.log(&#x27;⚠️ Requisição inválida: &#x27; + response.status);\n  } else {\n    cy.log(&#x27;ℹ️ Outro status: &#x27; + response.status);\n  }\n});\ncy.api({\n  method: &#x27;GET&#x27;,\n  url: &#x27;https://api-livenessv2.pay2freetech.com/liveness/validate-token&#x27;,\n  failOnStatusCode: false\n}).then(response =&gt; {\n  if (response.status === 200) {\n    cy.log(&#x27;✅ liveness/validate-token retornou 200&#x27;);\n  } else {\n    cy.log(`⚠️ liveness/validate-token retornou ${response.status}`);\n  }\n  cy.log(`📦 Body: ${JSON.stringify(response.body).substring(0, 300)}...`);\n});\ncy.api({\n  method: &#x27;GET&#x27;,\n  url: &#x27;https://api-livenessv2.pay2freetech.com/liveness/settings&#x27;,\n  failOnStatusCode: false\n}).then(response =&gt; {\n  if (response.status === 200) {\n    cy.log(&#x27;✅ liveness/validate-token retornou 200&#x27;);\n  } else {\n    cy.log(`⚠️ liveness/validate-token retornou ${response.status}`);\n  }\n  cy.log(`📦 Body: ${JSON.stringify(response.body).substring(0, 300)}...`);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;14b3b66c-f5cc-445e-a190-dada49e66800&quot;,&quot;parentUUID&quot;:&quot;e9c5afdb-d426-43b0-8df3-3161154c64f3&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[&quot;14b3b66c-f5cc-445e-a190-dada49e66800&quot;],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:30290,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000}],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:true,&quot;rootEmpty&quot;:true,&quot;_timeout&quot;:2000}],&quot;meta&quot;:{&quot;mocha&quot;:{&quot;version&quot;:&quot;7.2.0&quot;},&quot;mochawesome&quot;:{&quot;options&quot;:{&quot;quiet&quot;:false,&quot;reportFilename&quot;:&quot;[name]-report-[datetime]&quot;,&quot;saveHtml&quot;:false,&quot;saveJson&quot;:true,&quot;consoleReporter&quot;:&quot;spec&quot;,&quot;useInlineDiffs&quot;:false,&quot;code&quot;:true},&quot;version&quot;:&quot;7.1.3&quot;},&quot;marge&quot;:{&quot;options&quot;:{&quot;reportDir&quot;:&quot;cypress\\results\\.jsons&quot;,&quot;overwrite&quot;:false,&quot;html&quot;:false,&quot;json&quot;:true,&quot;reportFilename&quot;:&quot;[name]-report-[datetime]&quot;},&quot;version&quot;:&quot;6.2.0&quot;}}}" data-config="{&quot;reportFilename&quot;:&quot;[name]-report-[datetime]&quot;,&quot;reportDir&quot;:&quot;C:\\Users\\<USER>\\Documents\\EstrelaBet\\cypress\\results&quot;,&quot;reportTitle&quot;:&quot;EstrelaBet&quot;,&quot;reportPageTitle&quot;:&quot;Mochawesome Report&quot;,&quot;inline&quot;:false,&quot;inlineAssets&quot;:false,&quot;cdn&quot;:false,&quot;charts&quot;:false,&quot;enableCharts&quot;:false,&quot;code&quot;:true,&quot;enableCode&quot;:true,&quot;autoOpen&quot;:false,&quot;overwrite&quot;:true,&quot;timestamp&quot;:false,&quot;ts&quot;:false,&quot;showPassed&quot;:true,&quot;showFailed&quot;:true,&quot;showPending&quot;:true,&quot;showSkipped&quot;:false,&quot;showHooks&quot;:&quot;failed&quot;,&quot;saveJson&quot;:false,&quot;saveHtml&quot;:true,&quot;dev&quot;:false,&quot;assetsDir&quot;:&quot;C:\\Users\\<USER>\\Documents\\EstrelaBet\\cypress\\results\\assets&quot;,&quot;htmlFile&quot;:&quot;C:\\Users\\<USER>\\Documents\\EstrelaBet\\cypress\\results\\api-plataform-report-2025-09-05T162914-0300.html&quot;}"><div id="report"></div><script src="assets\app.js"></script></body></html>