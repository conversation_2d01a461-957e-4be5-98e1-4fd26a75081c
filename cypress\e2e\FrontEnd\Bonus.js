/// <reference types="cypress" />
import loc from '../../support/locators'
import { Utility } from "../../support/utility"
import '@cypress/grep'
import { blockTrackers } from '../../support/blockTrackers'


const url = new Utility().getBaseUrl();
const urlRequest = new Utility().getApiUrl();
const urlBff = new Utility().getApiBffUrl();

describe.skip('Cadastro', () => {
    beforeEach(() => {
        blockTrackers();
        cy.loginToAuth0IdSession(Cypress.env('user_name'), Cypress.env('user_password'))
    })
    it('Deve validar os elementos da tela de saque', () => {
        cy.get('.nebulosa-header__desktopButtonWrapper > .nebulosa-avatar__avatarContainer > .nebulosa-avatar__root > .nebulosa-avatar__fallback')
            .should('be.visible')
            .click();
    })
})