# EstrelaBet

Este é um projeto Cypress configurado para testes E2E.

## Pré-requisitos
- Node.js instalado (com npm e npx disponíveis no PATH)
- Python 3.x (para rodar este script de configuração)

## Como Rodar os Testes
1. Navegue até o diretório do projeto:
   ```
   cd EstrelaBet
   ```

2. Execute os testes via interface:
   ```
   npm run cypress:open:alfa
   ```

3. Execute os testes em modo headless:
   ```
   npm run cypress:run:alfa
   ```

## Estrutura Inicial
- `cypress/e2e`: Onde ficam os testes divididos por FrontEnd e Backend
- `cypress/support`: Comandos customizados e utilitários
- `cypress.config.js`: Arquivo de configuração principal

## Manual Cypress UDS
https://docs.google.com/document/d/1jur_rblZh_35tENh3RKXI283iK_qCbdr3Wgn9mOjk8U/edit?usp=sharing
