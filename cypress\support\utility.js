export class Utility {
 getBaseUrl() {
    let envi = Cypress.env('ENV'); // Get the value of the environment variable ENV
    if (envi == 'prod') { // Check the value
      return "https://www.estrelabet.bet.br";
    } else if (envi == 'hml') {
      return "https://hml.estrelabet.bet.br";
    } else if (envi == 'qa') {
      return "https://qa.estrelabet.bet.br";
    }
  }
  getApiBffUrl() {
    let envi = Cypress.env('ENV'); // Get the value of the environment variable ENV
    if (envi == 'prod') { // Check the value
      return "https://bff-estrelabet.estrelabet.bet.br";
    } else if (envi == 'hml') {
      return "https://bff-estrelabet.hml.estrelabet.bet.br";
    } else if (envi == 'qa') {
      return "https://bff-estrelabet.qa.estrelabet.bet.br";
    }
  }
  getApiUrl() {
    let envi = Cypress.env('ENV'); // Get the value of the environment variable ENV
    if (envi == 'prod') { // Check the value
      return "";
    } else if (envi == 'hml') {
      return "";
    } else if (envi == 'qa') {
      return "";
    }
  }
}
