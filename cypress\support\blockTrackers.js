export function blockTrackers() {
  
  // Performance tweaks
  Cypress.config('defaultCommandTimeout', 8000);
  Cypress.config('requestTimeout', 10000);
  Cypress.config('responseTimeout', 15000);

  // Block common third-party trackers/analytics/ads
  const blockedPatterns = [
    // Google Analytics & Ads
    /google-analytics/,
    /googleadservices\.com/,
    /pagead\./,
    /collect\?/,
    /gtm=/,
    /\/g\/collect/,
    /\/td\/rul\//,

    // Facebook Pixel
    /facebook\.com\/tr/,
    /\/tr\/\?id=/,
    /privacy_sandbox\/pixel/,

    // Taboola
    /taboola\.com/,
    /trc-events\.taboola\.com/,
    /\/trc\/3\//,
    /\/log\/3\/unip/,

    // DataDog Monitoring
    /api\/v2\/rum/,
    /api\/v2\/replay/,
    /api\/v2\/logs/,

    // Firebase
    /firebase:fetch/,
    /v1\/projects\/estrelabet/,

    // Other Tracking
    /ads\.mythad\.com/,
    /creativecdn\.com/,
    /unifiedPixel/,
    /collect\/radar/,
    /topics-membership/,
    /reportEvent/,
    /domainreliability\/upload/,
    /cdn-cgi\/rum/,
    /syncd\?/,
    /bsw_sync/,
    /\/m\/open/,
    /log\/common\/co\/api/,

    // TikTok Pixel
    /i18n\/pixel\/events\.js/,
    /kos\/s101/,

    // Generic
    /pixel/,
    /events\/[a-f0-9]{64}/,
  ];

  blockedPatterns.forEach((pattern) => {
    cy.intercept({ url: pattern }, { statusCode: 200, body: {}, log: false });
  });
}
