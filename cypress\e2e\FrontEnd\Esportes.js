/// <reference types="cypress" />

import loc from '../../support/locators';
import { Utility } from "../../support/utility"
import messages from '../../support/validationMessages'
import '@cypress/grep';
import { blockTrackers } from '../../support/blockTrackers'

const url = new Utility().getBaseUrl();
const urlRequest = new Utility().getApiUrl();

describe.skip('Deve validar opções da conta', () => {
    beforeEach(() => {
        blockTrackers()
        cy.loginToAuth0(Cypress.env('user_name'), Cypress.env('user_password'))

    })
    it.only('Validando componentes e textos da pagina no Sports Book Sidebar Esquerda', () => {
        // Acessa a página de esportes
        cy.contains('label > [href="/aposta-esportiva"]', 'Esportes').click();

        // Aguarda a página base e o iframe ficarem prontos
        cy.waitLoading()
        cy.waitForSportsIframeReady('iframe.aut-iframe', 'input[placeholder="Introduzir nome do time ou do campeonato"]')

        // Depois de pronto, valide o campo específico
        cy.switchToIframe('iframe.aut-iframe')
          .find('input[placeholder="Introduzir nome do time ou do campeonato"]', { timeout: 30000 })
          .should('be.visible')
          .and('have.attr', 'placeholder', 'Introduzir nome do time ou do campeonato')
    });
});

describe.skip('Deve validar opções da conta', () => {

    beforeEach(() => {
        // 1. Intercepta TODAS as requisições de página para o domínio
        cy.intercept('GET', 'https://hml.estrelabet.bet.br/**', (req) => {
            req.continue((res) => {
                if (res.headers['content-type'] && res.headers['content-type'].includes('text/html')) {
                    delete res.headers['x-frame-options'];
                }
            });
        });

        // 2. Visita a URL base (a variável 'url' deve estar definida no seu projeto)
        cy.visit(url);

        // Opcional mas recomendado: Aceitar cookies aqui se o banner sempre aparecer
        cy.waitLoading(); // Assumindo que é um comando customizado seu
        cy.get('body').then(($body) => {
            if ($body.find('button.nebulosa-button_root:contains("Aceitar todos os cookies")').length > 0) {
                cy.contains('Aceitar todos os cookies').click();
            }
        });
    });

    it('Validando componentes e textos da pagina', () => {
        cy.url().then(url => {
            cy.validateText('label > [href="/aposta-esportiva"]', 'Esportes').click()
            cy.url().should('eq', url + 'aposta-esportiva')
        })
        // 3. PRIMEIRO, entre no iframe. TODAS as ações seguintes serão dentro dele.
        // Use um seletor mais específico (iframe.aut-iframe) e aumente o timeout.
        /* cy.iframe('iframe.aut-iframe', { timeout: 20000 })
          .find('[label > [href="/aposta-esportiva"]]', { timeout: 15000 })
          .should('be.visible')
          .click(); */
        // Confirma que o iframe existe, está visível e carregado
        cy.get('iframe.aut-iframe', { timeout: 60000 }).should('be.visible')
        cy.get('iframe.aut-iframe', { timeout: 60000 })
          .should(($iframe) => {
            expect($iframe[0].contentDocument).to.exist
            expect($iframe[0].contentDocument.readyState).to.eq('complete')
          })

        // Alternativamente, espere por um seletor “sinal de pronto” dentro do iframe
        cy.switchToIframe('iframe.aut-iframe')
          .find('input[placeholder="Introduzir nome do time ou do campeonato"]', { timeout: 30000 })
          .should('be.visible')

        // 4. Agora, ainda dentro do iframe, a navegação para a página de esportes ocorreu.
        // Você pode continuar procurando outros elementos na nova página.
        // Por exemplo, verificar se um campo de busca está visível.
        /* cy.iframe('.aut-iframe')
          .find('input', { timeout: 15000 }) // Use um seletor mais específico para o que você quer validar
          .should('be.visible'); */
    });

});