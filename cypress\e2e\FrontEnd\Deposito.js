/// <reference types="cypress" />

import loc from '../../support/locators'
import { Utility } from "../../support/utility"
import messages from '../../support/validationMessages'
import '@cypress/grep';
import { blockTrackers } from '../../support/blockTrackers';

describe('Deve validar a geração de pix', () => {
    beforeEach(() => {
        // Bloqueia terceiros e aplica otimizações de performance
        blockTrackers();

        cy.loginToAuth0(Cypress.env('user_name'), Cypress.env('user_password'))

        cy.intercept({
            method: 'POST',
            url: 'https://bff-estrelabet.hml.estrelabet.bet.br/deposit',
        }).as('gerandoQrCode')

    });

    it('Gerar pix com sucesso', () => {
        cy.get('.nebulosa-header__buttomAndBadgeWrapper')
            .contains('button', 'Depositar')
        cy.clickByContains('.nebulosa-header__buttomAndBadgeWrapper', 'Depositar')

        //Validando modal de valores
        //Título
        cy.contains('h1', 'Depósito rápido').should('be.visible');
        //Título campo e valor pré selecionado
        cy.validateText('.nebulosa-input__Root > label', 'Valor do depósito')
        cy.get('.nebulosa-input__Input > input:eq(1)').should('have.value', 'R$ 50,00')
        //Validando opções disponíveis para depósito
        cy.validateText('.d_flex > div > h3', 'Valor mínimo R$ 1,00 e valor máximo R$ 45.000,00')
        cy.validateText('[type="button"] > span:eq(0)', 'R$ 10')
        cy.validateText('[type="button"] > span:eq(1)', 'R$ 50')
        cy.validateText('[type="button"] > span:eq(2)', 'R$ 100')
        cy.validateText('[type="button"] > span:eq(3)', 'R$ 250')
        cy.validateText('[type="button"] > span:eq(4)', 'R$ 500')
        cy.validateText('[type="button"] > span:eq(5)', 'R$ 1.000')
        //validando botão e mensagem de maior de 18
        cy.validateText('[type="submit"]', 'Depositar')
        //cy.validateText('form > div > .d_flex > .ai_center > p', 'Depósitos proibidos para menores de 18 anos.')
        //Botão x
        cy.isVisible('[data-testid="closeDepositModalButton"]')
        //Banner principal
        cy.isVisible('img[src*="deposit-banner.png"]')
        //clicando para avançar
        cy.clickMouse('[type="submit"]', 'Depositar')
        cy.wait('@gerandoQrCode', { timeout: 15000 })
            .its('response.statusCode')
            .should('eq', 200);

        //Label depósito/pix
        cy.clickByContains('h1', 'Código Pix disponível')
        cy.clickByContains('p', 'Copie o código e utilize o PIX Copia e Cola no aplicativo do seu banco.')
        cy.isVisible('button', 'Saiba como fazer o pagamento com Pix')
        cy.clickByContains('button', 'Saiba como fazer o pagamento com Pix')
        //Listagem com a informaços do Saiba como fazer o pagamento com Pix
        cy.get('ul > ul').eq(0).within(() => {
            cy.contains('p', '1. Acesse o aplicativo do seu banco e escolha a opção Pix Copia e Cola” e cole o código').should('be.visible')
        })
        cy.get('ul > ul').eq(1).within(() => {
            cy.contains('p', '2. Confirme as informações e finalize o pagamento').should('be.visible')
        })
        cy.get('ul > ul').eq(2).within(() => {
            cy.contains('p', '3. Seu pagamento será aprovado em alguns segundos!').should('be.visible')
        })

        cy.isVisible('p', 'Pagamentos feitos por outro CPF ou de contas empresariais serão automaticamente rejeitados')

        cy.isVisible('button', 'Exibir QR Code')
        cy.clickByContains('button', 'Exibir QR Code')

        cy.get('img').should('exist');

        // Captura o valor inicial do contador
        cy.get('span.fv-num_tabular-nums')
            .invoke('text')
            .then((initialValue) => {
                // Espera 1 segundo (ou o tempo que o contador deve mudar)
                cy.wait(1000);
                // Captura novamente e compara
                cy.get('span.fv-num_tabular-nums')
                    .invoke('text')
                    .should((newValue) => {
                        expect(newValue).to.not.eq(initialValue);
                    });
            });
        cy.validateText('.gap_md > .mb_xxxs > .fs_xxs', 'Valor do depósito:')
        cy.wait(2000)
        cy.contains('.mb_xxxs > .fs_xs', 'R$ 50,00').should('be.visible')

        //botao Copiar código Pix
        cy.contains('button', 'Copiar código Pix')
            .should('have.attr', 'lefticon', 'brand-pix')

        cy.stubClipboard();
        cy.clickMouse('[lefticon="brand-pix"] > span')

        // validar que tentou copiar:
        cy.get('@clipboardWrite').should('have.been.called')
        //Validar mensagem de sucesso
        cy.get('div.nebulosa-toast__descriptionText')
            .should('be.visible')
            .and('contain.text', 'Código PIX copiado!')
    });
})