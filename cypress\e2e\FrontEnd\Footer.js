/// <reference types="cypress" />

import loc from '../../support/locators'
import { Utility } from "../../support/utility"
import '@cypress/grep'
import { blockTrackers } from '../../support/blockTrackers'


const url = new Utility().getBaseUrl();

describe('Deve abrir a página de cassino', () => {

    it('Valida elementos do footer', () => {
        blockTrackers();
        cy.visit(url)

        cy.get('img[alt="Estrelabet"]').should('be.visible')

        cy.get('a[href*="facebook.com"] img[alt="facebook"]').should('be.visible')
        cy.get('a[href*="twitter.com"] img[alt="twitter"]').should('be.visible')
        cy.get('a[href*="youtube.com"] img[alt="youtube"]').should('be.visible')
        cy.get('a[href*="instagram.com"] img[alt="instagram"]').should('be.visible')
        cy.get('a[href*="t.me"] img[alt="telegram"]').should('be.visible')
        cy.get('a[href*="tiktok.com"] img[alt="tiktok"]').should('be.visible')
        cy.get('a[href*="blog.estrelabet.com"] img[alt="blog"]').should('be.visible')


        cy.contains('span', 'Baixe o Aplicativo EstrelaBet').should('be.visible')
        cy.get('img[alt="Disponível no Google Play"]').should('be.visible')
        cy.get('a[href*="play.google.com"]').should('have.attr', 'href')
            .and('include', 'play.google.com')

        // "Aposte"
        cy.contains("span", "Aposte").should("be.visible")
        // 19/08/2025 mudaram o locator
        //cy.validateText('.StaticFooter_footer-links-section__0Fi_h', 'Aposte')

        cy.validateText('[href="https://www.estrelabet.bet.br/aposta-esportiva"]', 'Apostas esportivas')
        cy.validateText('[href="https://www.estrelabet.bet.br/gameplay/fortune-tiger"]', 'Fortune Tiger')
        cy.validateText('[href="https://www.estrelabet.bet.br/gameplay/fortune-rabbit"]', 'Fortune Rabbit')
        cy.validateText('[href="https://play.google.com/store/apps/details?id=co.br.bet.estrelabet.app&pli=1"]', 'App de Apostas')
        // "Links úteis"
        //cy.validateText('.StaticFooter_footer-links-header__U2WL_', 'Links úteis')
        cy.contains("span", "Links úteis").should("be.visible")

        cy.validateText('[href="https://comunidade.estrelabet.com/"]', 'Comunidade')
        cy.validateText('[href="https://www.estrelabet.bet.br/pb/offers"]', 'Ofertas')
        cy.validateText('[href="https://www.estrelabet.bet.br/page/responsible-gaming"] > span:eq(0)', 'Jogo responsável')
        cy.validateText('[href="https://blog.estrelabet.com/"]  > span:eq(0)', 'Blog')

        // "Regras"
        cy.contains("span", "Regras").should("be.visible");
        //cy.validateText('.StaticFooter_footer-links-header__U2WL_', 'Regras')

        cy.validateText('[href="https://www.estrelabet.bet.br/pb/politica/termos-e-condicoes"]', 'Termos e Condições Gerais') //[href="https://www.estrelabet.bet.br/politica/termos-e-condicoes
        cy.validateText('[href="https://www.estrelabet.bet.br/page/responsible-gaming"] > span:eq(1)', 'Jogo responsável')
        cy.validateText('[href="https://www.estrelabet.bet.br/policy/sports-betting-rules"]', 'Regras de apostas esportivas')
        cy.validateText('[href="https://www.estrelabet.bet.br/policy/bonus-rules"]', 'Termos e condições gerais de bônus')
        cy.validateText('[href="https://www.estrelabet.bet.br/policy/privacy-policy"]', 'Política de privacidade')

        // "Suporte"
        //cy.validateText('.StaticFooter_footer-links-header__U2WL_', 'Suporte')
        cy.contains("span", "Suporte").should("be.visible")

        cy.validateText('[href="https://estrelabet.zendesk.com/hc"]', 'Central de ajuda')
        cy.validateText('[href="tel:0800 000 4546"]', '0800 000 4546')
        cy.validateText('[href="mailto:<EMAIL>"]', '<EMAIL>')

        // "Outros"
        //cy.validateText('.StaticFooter_footer-links-header__U2WL_', 'Outros')
        cy.contains("span", "Outros").should("be.visible")

        cy.validateText('[href="https://estrela-bet.atlassian.net/helpcenter/ouvidoria/"]', 'Ouvidoria')
        cy.validateText('[href="https://consumidor2.procon.sp.gov.br/login"]', 'Procon')
        cy.validateText('[href="https://www.planalto.gov.br/ccivil_03/leis/l8078compilado.htm"]', 'Código de Defesa do Consumidor')

        // "Patrocinadores oficiais"
        cy.contains('span', 'PATROCINADOR OFICIAL').should('be.visible')
        cy.get('img[alt="Criciúma Esporte Clube"]').should('be.visible')
        cy.get('img[alt="Villa Nova Atlético Clube"]').should('be.visible')
        cy.get('img[alt="Vivo Keyd Stars"]').should('be.visible')

        // "EstrelaBet Awards"
        cy.contains('span', 'ESTRELABET AWARDS').should('be.visible')
        cy.get('img[alt="Sigma Awards"]').should('be.visible');
        cy.get('img[alt="BiS Awards"]').should('be.visible');
        cy.get('img[alt="SBC Awards"]').should('be.visible');
        cy.get('img[alt="CGS Awards"]').should('be.visible');

        // "Aviso legal"
        cy.contains('O jogo, se não controlado e feito com responsabilidade, pode ser prejudicial.')
            .should('be.visible');
        cy.contains('a span', 'Jogo responsável.').should('be.visible');

        // "Informações legais"
        cy.contains('Autorização SPA/MF nº 320/2025').should('be.visible');
        cy.contains('Aposte com responsabilidade').should('be.visible');
        cy.contains('Aposta não é investimento').should('be.visible');
        cy.contains('Este site é operado por EB INTERMEDIAÇÕES E JOGOS S/A').should('be.visible');
        cy.contains('CNPJ sob o nº 52.639.845/0001-25').should('be.visible');
        cy.get('img[alt="ibrj-instituto-brasileiro-jogo-responsavel"]').should('be.visible');
        cy.get('img[alt="lexis-nexis"]').should('be.visible');
        cy.get('img[alt="sportsradar"]').should('be.visible');
    });
});