/// <reference types="cypress" />

describe.skip('Deve validar login', () => {
    it('Deve validar login com sucesso.', () => {
        cy.request({
            method: 'POST',
            url: 'https://cognito-idp.us-east-1.amazonaws.com/',
            headers: {
                'content-type': 'application/x-amz-json-1.1',
                'x-amz-target': 'AWSCognitoIdentityProviderService.InitiateAuth'
            },
            body: {
                AuthFlow: "USER_PASSWORD_AUTH",
                ClientId: "39freu20si9ss1tmsggcov5nrq",
                AuthParameters: {
                    USERNAME: Cypress.env('user_name'),
                    PASSWORD: Cypress.env('user_password')
                },
                "ClientMetadata": {}
            },
        }).then((response) => {
            expect(response.status).to.eq(200);
            expect(response.body.AuthenticationResult.AccessToken).to.not.empty
            expect(response.body.AuthenticationResult.ExpiresIn).to.eq(86400)
            expect(response.body.AuthenticationResult.IdToken).to.not.empty
            expect(response.body.AuthenticationResult.RefreshToken).to.not.empty
            expect(response.body.AuthenticationResult.TokenType).to.eq('Bearer')
            expect(response.body.ChallengeParameters).to.empty
        })
    });
    it('Deve validar login incorreto', () => {
        cy.request({
            method: 'POST',
            url: 'https://cognito-idp.us-east-1.amazonaws.com/',
            headers: {
                'content-type': 'application/x-amz-json-1.1',
                'x-amz-target': 'AWSCognitoIdentityProviderService.InitiateAuth'
            },
            body: {
                AuthFlow: "USER_PASSWORD_AUTH",
                ClientId: "3103ticv585k9u5mha4c6ckdq9",
                AuthParameters: {
                    USERNAME: Cypress.env('user_name'),
                    PASSWORD: '123321'
                },
                "ClientMetadata": {}
            },
            failOnStatusCode: false
        }).then((response) => {
            expect(response.status).to.eq(400);
            expect(response.body.__type).to.eq('NotAuthorizedException')
            expect(response.body.message).to.eq('Incorrect username or password.')
        })
    });
});